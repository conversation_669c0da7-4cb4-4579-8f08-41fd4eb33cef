# OBD2 AI Diagnostic System Environment Configuration

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
LOG_FILE=logs/obd_diagnostics.log
SECRET_KEY=your-secret-key-change-this-in-production

# Database Configuration
DATABASE_URL=sqlite:///./obd_diagnostics.db
REDIS_URL=redis://localhost:6379

# OBD2 Configuration
OBD_PORT=/dev/ttyUSB0
OBD_BAUDRATE=38400
OBD_TIMEOUT=30.0
CAN_INTERFACE=can0
CAN_BITRATE=500000

# AI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
USE_LOCAL_LLM=false
LOCAL_LLM_MODEL_PATH=/path/to/local/model.bin

# External APIs
VEHICLE_API_KEY=your-vehicle-api-key

# Security
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Performance
MAX_CONCURRENT_SCANS=5
CACHE_TTL_SECONDS=300

# Features
ENABLE_REAL_TIME_MONITORING=true
ENABLE_COST_ESTIMATION=true
ENABLE_MAINTENANCE_PLANNING=true
ENABLE_BRAND_SPECIFIC_ANALYSIS=true

# Logging
ENABLE_AUDIT_LOG=true
AUDIT_LOG_FILE=logs/audit.log

# Backup
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
