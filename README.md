# OBD2 AI Diagnostic System

A comprehensive Python-based OBD2 diagnostic system with AI-powered analysis and brand-specific solutions.

## 🚗 Features

### Core Functionality
- **OBD2 Communication**: Support for ELM327 adapters via USB/Bluetooth
- **CAN Bus Integration**: Direct ECU communication using UDS/KWP2000 protocols
- **DTC Analysis**: Comprehensive diagnostic trouble code interpretation
- **AI-Powered Diagnostics**: OpenAI GPT integration for intelligent analysis
- **Brand-Specific Solutions**: Specialized profiles for Toyota, VAG Group, and BMW

### Advanced Capabilities
- **Real-time Monitoring**: Live parameter monitoring and data logging
- **Cost Estimation**: AI-powered repair cost estimation
- **Maintenance Planning**: Predictive maintenance recommendations
- **Multi-Protocol Support**: OBD2, UDS, KWP2000, and manufacturer-specific protocols
- **RESTful API**: Complete FastAPI-based web service

## 🏗️ Architecture

```
app/
├── obd_interface/          # OBD2 and CAN communication
│   ├── obd_reader.py      # ELM327 interface
│   ├── can_reader.py      # CAN/UDS interface
│   └── dtc_parser.py      # DTC interpretation
├── ai_engine/             # AI analysis engine
│   ├── prompt_builder.py  # AI prompt generation
│   └── explain_dtc.py     # AI-powered analysis
├── brand_profiles/        # Vehicle brand-specific logic
│   ├── toyota.py          # Toyota/Lexus profiles
│   ├── vag.py            # VW/Audi/Skoda/Seat profiles
│   └── bmw.py            # BMW/MINI profiles
├── api/                   # FastAPI web service
│   ├── models.py         # Pydantic models
│   └── routes.py         # API endpoints
├── data/                  # Static data and databases
│   ├── dtc_codes.json    # DTC database
│   └── ecu_parameters.json # ECU parameter definitions
└── utils/                 # Utility functions
    └── helpers.py        # Common utilities
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- OBD2 adapter (ELM327 compatible)
- Optional: CAN interface for advanced diagnostics

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd machina
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Run the application**
```bash
python -m uvicorn app.main:app --reload
```

### Docker Deployment

1. **Using Docker Compose**
```bash
docker-compose up -d
```

2. **Access the API**
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## 📡 API Usage

### Connect to OBD2 Adapter
```bash
curl -X POST "http://localhost:8000/api/v1/connect" \
  -H "Content-Type: application/json" \
  -d '{
    "connection_type": "usb",
    "port": "/dev/ttyUSB0",
    "baudrate": 38400
  }'
```

### Perform Diagnostic Scan
```bash
curl -X POST "http://localhost:8000/api/v1/scan" \
  -H "Content-Type: application/json" \
  -d '{
    "include_dtcs": true,
    "include_parameters": true,
    "include_freeze_frame": false
  }'
```

### AI Analysis
```bash
curl -X POST "http://localhost:8000/api/v1/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "vehicle_info": {
      "make": "Toyota",
      "model": "Prius",
      "year": 2015
    },
    "include_brand_specific": true,
    "include_cost_estimate": true
  }'
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DEBUG` | Enable debug mode | `false` |
| `OBD_PORT` | OBD2 adapter port | `/dev/ttyUSB0` |
| `OPENAI_API_KEY` | OpenAI API key | None |
| `USE_LOCAL_LLM` | Use local LLM instead of OpenAI | `false` |
| `CAN_INTERFACE` | CAN interface name | `can0` |

### OBD2 Adapter Setup

1. **USB Connection**
   - Connect ELM327 adapter to USB port
   - Verify device appears as `/dev/ttyUSB0` (Linux) or `COM3` (Windows)

2. **Bluetooth Connection**
   - Pair ELM327 adapter with system
   - Use appropriate Bluetooth serial port

3. **CAN Interface**
   - Configure CAN interface: `sudo ip link set can0 type can bitrate 500000`
   - Bring up interface: `sudo ip link set up can0`

## 🧠 AI Integration

### OpenAI Configuration
```bash
export OPENAI_API_KEY="your-api-key-here"
export OPENAI_MODEL="gpt-3.5-turbo"
```

### Local LLM Support
```bash
export USE_LOCAL_LLM=true
export LOCAL_LLM_MODEL_PATH="/path/to/model.bin"
```

## 🚗 Supported Vehicles

### Toyota/Lexus
- **Models**: Prius, Camry, Corolla, RAV4, Highlander, Lexus ES/RX/NX
- **Special Features**: Hybrid system diagnostics, VVT-i analysis
- **Known Issues**: Oil consumption, hybrid battery health, EGR problems

### VAG Group (VW/Audi/Skoda/Seat)
- **Models**: Golf, Jetta, Passat, A3, A4, A6, Octavia
- **Special Features**: DSG diagnostics, DPF regeneration, VCDS integration
- **Known Issues**: Carbon buildup, timing chain, DPF clogging

### BMW/MINI
- **Models**: 3/5/7 Series, X1/X3/X5, MINI Cooper
- **Special Features**: Valvetronic diagnostics, VANOS analysis, ISTA integration
- **Known Issues**: HPFP failure, timing chain stretch, water pump

## 📊 Data Sources

### Open Source References
- **Toyota**: [OBD-PIDs-for-Hybrid-Cars](https://github.com/iwanders/OBD-PIDs-for-Hybrid-Cars)
- **VAG Group**: [vag-obd](https://github.com/M0RtY/vag-obd)
- **BMW**: [ediabaslib](https://github.com/uholeschak/ediabaslib)

### DTC Database
- Comprehensive DTC definitions with causes and solutions
- Brand-specific interpretations and procedures
- Freeze frame data analysis

## 🧪 Testing

### Unit Tests
```bash
pytest tests/ -v
```

### Integration Tests
```bash
pytest tests/integration/ -v
```

### OBD2 Simulator
```bash
# Install OBD2 simulator for testing
pip install obd-simulator
python -m obd_simulator --port /dev/pts/1
```

## 📈 Monitoring

### Metrics
- API response times
- Diagnostic scan success rates
- AI analysis accuracy
- System resource usage

### Logging
- Structured logging with loguru
- Audit trail for all operations
- Error tracking and alerting

## 🔒 Security

### Best Practices
- API key management
- Input validation
- Rate limiting
- Secure communication

### Data Privacy
- No VIN storage without consent
- Encrypted data transmission
- GDPR compliance considerations

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run pre-commit hooks
pre-commit install

# Run tests
pytest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- API Documentation: `/docs`
- OpenAPI Spec: `/openapi.json`

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Wiki for additional documentation

### Commercial Support
Contact for enterprise support and custom integrations.

## 🗺️ Roadmap

### Phase 1 (Current)
- ✅ Basic OBD2 communication
- ✅ AI-powered analysis
- ✅ Brand-specific profiles
- ✅ RESTful API

### Phase 2 (Planned)
- 🔄 Web dashboard
- 🔄 Mobile app integration
- 🔄 Advanced CAN diagnostics
- 🔄 Machine learning models

### Phase 3 (Future)
- 📋 Predictive maintenance
- 📋 Fleet management
- 📋 Blockchain integration
- 📋 IoT connectivity

---

**Built with ❤️ for the automotive community**
