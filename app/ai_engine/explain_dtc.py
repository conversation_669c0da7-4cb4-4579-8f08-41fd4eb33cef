"""
AI-powered DTC explanation and solution engine
Integrates with OpenAI API and local LLM options
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import json

# OpenAI integration
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Local LLM integration
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False

from .prompt_builder import PromptBuilder, DiagnosticContext, VehicleContext
from ..obd_interface.dtc_parser import DTCInfo
from ..config import settings


logger = logging.getLogger(__name__)


@dataclass
class AIResponse:
    """AI response data structure"""
    content: str
    model_used: str
    tokens_used: Optional[int] = None
    cost_estimate: Optional[float] = None
    processing_time: Optional[float] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class DiagnosticAnalysis:
    """Complete diagnostic analysis result"""
    summary: str
    priority_issues: List[Dict[str, Any]]
    repair_recommendations: List[Dict[str, Any]]
    cost_estimates: Dict[str, Any]
    safety_warnings: List[str]
    preventive_measures: List[str]
    confidence_score: float
    ai_response: AIResponse


class DTCExplainer:
    """
    AI-powered DTC explanation and diagnostic analysis engine
    """
    
    def __init__(self):
        self.prompt_builder = PromptBuilder()
        self.openai_client = None
        self.local_llm = None
        self._initialize_ai_engines()
    
    def _initialize_ai_engines(self):
        """Initialize available AI engines"""
        # Initialize OpenAI
        if OPENAI_AVAILABLE and settings.openai_api_key:
            try:
                openai.api_key = settings.openai_api_key
                self.openai_client = openai
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI: {e}")
        
        # Initialize local LLM
        if LLAMA_CPP_AVAILABLE and settings.use_local_llm and settings.local_llm_model_path:
            try:
                self.local_llm = Llama(
                    model_path=settings.local_llm_model_path,
                    n_ctx=4096,  # Context window
                    n_threads=4,  # CPU threads
                    verbose=False
                )
                logger.info("Local LLM initialized")
            except Exception as e:
                logger.error(f"Failed to initialize local LLM: {e}")
    
    async def explain_dtcs(self, diagnostic_context: DiagnosticContext) -> DiagnosticAnalysis:
        """
        Generate comprehensive DTC explanation and analysis
        """
        start_time = datetime.now()
        
        try:
            # Build the analysis prompt
            prompt = self.prompt_builder.build_dtc_analysis_prompt(diagnostic_context)
            
            # Get AI response
            ai_response = await self._get_ai_response(prompt)
            
            # Parse the response into structured analysis
            analysis = self._parse_ai_response(ai_response, diagnostic_context)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            analysis.ai_response.processing_time = processing_time
            
            logger.info(f"DTC analysis completed in {processing_time:.2f} seconds")
            return analysis
            
        except Exception as e:
            logger.error(f"Error in DTC explanation: {e}")
            return self._create_fallback_analysis(diagnostic_context)
    
    async def explain_brand_specific(self, diagnostic_context: DiagnosticContext, brand_knowledge: Dict[str, Any]) -> DiagnosticAnalysis:
        """
        Generate brand-specific DTC explanation
        """
        try:
            prompt = self.prompt_builder.build_brand_specific_prompt(diagnostic_context, brand_knowledge)
            ai_response = await self._get_ai_response(prompt)
            analysis = self._parse_ai_response(ai_response, diagnostic_context)
            
            # Add brand-specific confidence boost
            analysis.confidence_score = min(analysis.confidence_score + 0.1, 1.0)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in brand-specific explanation: {e}")
            return await self.explain_dtcs(diagnostic_context)
    
    async def estimate_repair_costs(self, diagnostic_context: DiagnosticContext, repair_recommendations: List[str]) -> Dict[str, Any]:
        """
        Generate repair cost estimates using AI
        """
        try:
            prompt = self.prompt_builder.build_cost_estimation_prompt(diagnostic_context, repair_recommendations)
            ai_response = await self._get_ai_response(prompt)
            
            # Parse cost information from response
            cost_data = self._parse_cost_response(ai_response.content)
            return cost_data
            
        except Exception as e:
            logger.error(f"Error in cost estimation: {e}")
            return self._create_fallback_cost_estimate()
    
    async def generate_maintenance_plan(self, diagnostic_context: DiagnosticContext) -> Dict[str, Any]:
        """
        Generate maintenance recommendations using AI
        """
        try:
            prompt = self.prompt_builder.build_maintenance_prompt(diagnostic_context)
            ai_response = await self._get_ai_response(prompt)
            
            # Parse maintenance plan from response
            maintenance_plan = self._parse_maintenance_response(ai_response.content)
            return maintenance_plan
            
        except Exception as e:
            logger.error(f"Error in maintenance plan generation: {e}")
            return self._create_fallback_maintenance_plan()
    
    async def _get_ai_response(self, prompt: str) -> AIResponse:
        """
        Get response from available AI engine
        """
        # Try OpenAI first
        if self.openai_client and not settings.use_local_llm:
            return await self._get_openai_response(prompt)
        
        # Try local LLM
        elif self.local_llm:
            return await self._get_local_llm_response(prompt)
        
        # Fallback to rule-based response
        else:
            return self._get_fallback_response(prompt)
    
    async def _get_openai_response(self, prompt: str) -> AIResponse:
        """
        Get response from OpenAI API
        """
        try:
            response = await asyncio.to_thread(
                self.openai_client.ChatCompletion.create,
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert automotive diagnostic technician."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            # Estimate cost (approximate)
            cost_per_token = 0.002 / 1000  # $0.002 per 1K tokens for GPT-3.5-turbo
            cost_estimate = tokens_used * cost_per_token
            
            return AIResponse(
                content=content,
                model_used=settings.openai_model,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate
            )
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def _get_local_llm_response(self, prompt: str) -> AIResponse:
        """
        Get response from local LLM
        """
        try:
            response = await asyncio.to_thread(
                self.local_llm,
                prompt,
                max_tokens=2000,
                temperature=0.3,
                stop=["Human:", "Assistant:"]
            )
            
            content = response['choices'][0]['text']
            
            return AIResponse(
                content=content,
                model_used="Local LLM",
                tokens_used=None,
                cost_estimate=0.0
            )
            
        except Exception as e:
            logger.error(f"Local LLM error: {e}")
            raise
    
    def _get_fallback_response(self, prompt: str) -> AIResponse:
        """
        Generate fallback response using rule-based logic
        """
        content = """Based on the diagnostic information provided, here is a basic analysis:

1. ISSUE SUMMARY:
   - Multiple diagnostic trouble codes detected
   - Requires professional diagnosis for accurate assessment
   - Recommend comprehensive vehicle inspection

2. REPAIR PRIORITY:
   - Consult with qualified technician for priority assessment
   - Address any safety-related issues immediately

3. DETAILED RECOMMENDATIONS:
   - Professional diagnostic scan recommended
   - Follow manufacturer service guidelines
   - Consider vehicle age and mileage in repair decisions

4. PREVENTIVE MEASURES:
   - Regular maintenance per manufacturer schedule
   - Monitor vehicle performance for changes
   - Address issues promptly to prevent escalation

5. SAFETY CONSIDERATIONS:
   - If warning lights are active, seek immediate service
   - Monitor vehicle behavior for any changes
   - Do not ignore persistent symptoms

Note: This is a basic analysis. For accurate diagnosis and repair recommendations, consult with a qualified automotive technician."""
        
        return AIResponse(
            content=content,
            model_used="Rule-based fallback",
            tokens_used=None,
            cost_estimate=0.0
        )
    
    def _parse_ai_response(self, ai_response: AIResponse, diagnostic_context: DiagnosticContext) -> DiagnosticAnalysis:
        """
        Parse AI response into structured diagnostic analysis
        """
        content = ai_response.content
        
        # Extract sections using simple text parsing
        summary = self._extract_section(content, "ISSUE SUMMARY", "REPAIR PRIORITY")
        priority_text = self._extract_section(content, "REPAIR PRIORITY", "DETAILED RECOMMENDATIONS")
        recommendations_text = self._extract_section(content, "DETAILED RECOMMENDATIONS", "PREVENTIVE MEASURES")
        preventive_text = self._extract_section(content, "PREVENTIVE MEASURES", "SAFETY CONSIDERATIONS")
        safety_text = self._extract_section(content, "SAFETY CONSIDERATIONS", None)
        
        # Parse priority issues
        priority_issues = self._parse_priority_issues(priority_text, diagnostic_context.dtcs)
        
        # Parse repair recommendations
        repair_recommendations = self._parse_repair_recommendations(recommendations_text)
        
        # Extract safety warnings
        safety_warnings = self._parse_safety_warnings(safety_text)
        
        # Extract preventive measures
        preventive_measures = self._parse_preventive_measures(preventive_text)
        
        # Calculate confidence score based on available data
        confidence_score = self._calculate_confidence_score(diagnostic_context, ai_response)
        
        return DiagnosticAnalysis(
            summary=summary or "Diagnostic analysis completed",
            priority_issues=priority_issues,
            repair_recommendations=repair_recommendations,
            cost_estimates={},  # Will be filled by separate cost estimation
            safety_warnings=safety_warnings,
            preventive_measures=preventive_measures,
            confidence_score=confidence_score,
            ai_response=ai_response
        )
    
    def _extract_section(self, content: str, start_marker: str, end_marker: Optional[str]) -> str:
        """Extract a section from AI response content"""
        try:
            start_idx = content.find(start_marker)
            if start_idx == -1:
                return ""
            
            start_idx = content.find(":", start_idx) + 1
            
            if end_marker:
                end_idx = content.find(end_marker, start_idx)
                if end_idx == -1:
                    return content[start_idx:].strip()
                return content[start_idx:end_idx].strip()
            else:
                return content[start_idx:].strip()
        except:
            return ""
    
    def _parse_priority_issues(self, priority_text: str, dtcs: List[DTCInfo]) -> List[Dict[str, Any]]:
        """Parse priority issues from AI response"""
        issues = []
        
        # Map severity levels
        severity_map = {
            "critical": 1,
            "high": 2,
            "medium": 3,
            "low": 4
        }
        
        for dtc in dtcs:
            issue = {
                "code": dtc.code,
                "description": dtc.description,
                "severity": dtc.severity,
                "priority": severity_map.get(dtc.severity.lower(), 5),
                "system": dtc.system
            }
            issues.append(issue)
        
        # Sort by priority
        issues.sort(key=lambda x: x["priority"])
        
        return issues
    
    def _parse_repair_recommendations(self, recommendations_text: str) -> List[Dict[str, Any]]:
        """Parse repair recommendations from AI response"""
        recommendations = []
        
        # Simple parsing - look for bullet points or numbered items
        lines = recommendations_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or any(line.startswith(f"{i}.") for i in range(1, 10))):
                # Clean up the line
                clean_line = line.lstrip('-•0123456789. ').strip()
                if clean_line:
                    recommendations.append({
                        "description": clean_line,
                        "priority": "medium",  # Default priority
                        "estimated_time": "TBD",
                        "estimated_cost": "TBD"
                    })
        
        return recommendations
    
    def _parse_safety_warnings(self, safety_text: str) -> List[str]:
        """Parse safety warnings from AI response"""
        warnings = []
        
        lines = safety_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or "warning" in line.lower() or "danger" in line.lower()):
                clean_line = line.lstrip('-•0123456789. ').strip()
                if clean_line:
                    warnings.append(clean_line)
        
        return warnings
    
    def _parse_preventive_measures(self, preventive_text: str) -> List[str]:
        """Parse preventive measures from AI response"""
        measures = []
        
        lines = preventive_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or any(line.startswith(f"{i}.") for i in range(1, 10))):
                clean_line = line.lstrip('-•0123456789. ').strip()
                if clean_line:
                    measures.append(clean_line)
        
        return measures
    
    def _calculate_confidence_score(self, diagnostic_context: DiagnosticContext, ai_response: AIResponse) -> float:
        """Calculate confidence score for the analysis"""
        score = 0.5  # Base score
        
        # Boost for having DTCs
        if diagnostic_context.dtcs:
            score += 0.2
        
        # Boost for having vehicle info
        if diagnostic_context.vehicle_info and diagnostic_context.vehicle_info.make:
            score += 0.1
        
        # Boost for having parameters
        if diagnostic_context.parameters:
            score += 0.1
        
        # Boost for using advanced AI
        if ai_response.model_used != "Rule-based fallback":
            score += 0.1
        
        return min(score, 1.0)
    
    def _parse_cost_response(self, content: str) -> Dict[str, Any]:
        """Parse cost estimation from AI response"""
        return {
            "total_estimate_low": 0,
            "total_estimate_high": 0,
            "breakdown": [],
            "notes": content[:500] + "..." if len(content) > 500 else content
        }
    
    def _parse_maintenance_response(self, content: str) -> Dict[str, Any]:
        """Parse maintenance plan from AI response"""
        return {
            "immediate": [],
            "short_term": [],
            "long_term": [],
            "notes": content[:500] + "..." if len(content) > 500 else content
        }
    
    def _create_fallback_analysis(self, diagnostic_context: DiagnosticContext) -> DiagnosticAnalysis:
        """Create fallback analysis when AI is unavailable"""
        fallback_response = self._get_fallback_response("")
        return self._parse_ai_response(fallback_response, diagnostic_context)
    
    def _create_fallback_cost_estimate(self) -> Dict[str, Any]:
        """Create fallback cost estimate"""
        return {
            "total_estimate_low": 100,
            "total_estimate_high": 500,
            "breakdown": [{"item": "Professional diagnosis", "cost_low": 100, "cost_high": 200}],
            "notes": "Consult with local service provider for accurate estimates"
        }
    
    def _create_fallback_maintenance_plan(self) -> Dict[str, Any]:
        """Create fallback maintenance plan"""
        return {
            "immediate": ["Professional diagnostic scan"],
            "short_term": ["Follow manufacturer maintenance schedule"],
            "long_term": ["Regular inspections and preventive maintenance"],
            "notes": "Consult vehicle manual and service provider for specific recommendations"
        }
