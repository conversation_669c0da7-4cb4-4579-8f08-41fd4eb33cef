"""
AI Prompt Builder for generating contextual prompts for LLM analysis
"""
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from ..obd_interface.dtc_parser import DTCInfo
from ..obd_interface.obd_reader import OBDParameter


@dataclass
class VehicleContext:
    """Vehicle context information for AI analysis"""
    make: Optional[str] = None
    model: Optional[str] = None
    year: Optional[int] = None
    vin: Optional[str] = None
    engine_type: Optional[str] = None
    mileage: Optional[int] = None
    fuel_type: Optional[str] = None


@dataclass
class DiagnosticContext:
    """Diagnostic context for AI analysis"""
    dtcs: List[DTCInfo]
    parameters: List[OBDParameter]
    vehicle_info: VehicleContext
    symptoms: Optional[str] = None
    recent_repairs: Optional[str] = None
    driving_conditions: Optional[str] = None


class PromptBuilder:
    """
    Builds contextual prompts for AI-powered diagnostic analysis
    """
    
    def __init__(self):
        self.base_system_prompt = self._get_base_system_prompt()
    
    def _get_base_system_prompt(self) -> str:
        """
        Get the base system prompt for automotive diagnostics
        """
        return """You are an expert automotive diagnostic technician with extensive knowledge of:
- OBD2 diagnostic trouble codes (DTCs)
- Vehicle systems (engine, transmission, emissions, etc.)
- Repair procedures and troubleshooting
- Brand-specific issues and solutions
- Cost-effective repair strategies

Your role is to analyze diagnostic data and provide:
1. Clear explanations of detected issues
2. Prioritized repair recommendations
3. Estimated costs and time requirements
4. Preventive maintenance suggestions
5. Safety considerations

Always consider:
- Vehicle make, model, and year
- Mileage and maintenance history
- Driving conditions and symptoms
- Cost-effectiveness of repairs
- Safety implications

Provide practical, actionable advice that a vehicle owner can understand."""
    
    def build_dtc_analysis_prompt(self, diagnostic_context: DiagnosticContext) -> str:
        """
        Build a prompt for DTC analysis
        """
        prompt_parts = [
            self.base_system_prompt,
            "\n\n=== DIAGNOSTIC ANALYSIS REQUEST ===\n"
        ]
        
        # Vehicle information
        if diagnostic_context.vehicle_info:
            vehicle_info = self._format_vehicle_info(diagnostic_context.vehicle_info)
            prompt_parts.append(f"VEHICLE INFORMATION:\n{vehicle_info}\n")
        
        # DTC information
        if diagnostic_context.dtcs:
            dtc_info = self._format_dtc_info(diagnostic_context.dtcs)
            prompt_parts.append(f"DIAGNOSTIC TROUBLE CODES:\n{dtc_info}\n")
        
        # Parameter data
        if diagnostic_context.parameters:
            param_info = self._format_parameter_info(diagnostic_context.parameters)
            prompt_parts.append(f"CURRENT PARAMETERS:\n{param_info}\n")
        
        # Additional context
        if diagnostic_context.symptoms:
            prompt_parts.append(f"REPORTED SYMPTOMS:\n{diagnostic_context.symptoms}\n")
        
        if diagnostic_context.recent_repairs:
            prompt_parts.append(f"RECENT REPAIRS:\n{diagnostic_context.recent_repairs}\n")
        
        if diagnostic_context.driving_conditions:
            prompt_parts.append(f"DRIVING CONDITIONS:\n{diagnostic_context.driving_conditions}\n")
        
        # Analysis request
        analysis_request = """
PLEASE PROVIDE:

1. ISSUE SUMMARY:
   - Primary problems identified
   - Root cause analysis
   - Interconnected issues

2. REPAIR PRIORITY:
   - Critical (immediate attention)
   - High (service soon)
   - Medium (schedule maintenance)
   - Low (monitor)

3. DETAILED RECOMMENDATIONS:
   - Specific repair procedures
   - Parts likely needed
   - Estimated costs (labor + parts)
   - Time requirements

4. PREVENTIVE MEASURES:
   - How to prevent recurrence
   - Maintenance schedule adjustments
   - Driving habit recommendations

5. SAFETY CONSIDERATIONS:
   - Is it safe to drive?
   - Any immediate precautions
   - Warning signs to watch for

Format your response in clear sections with bullet points for easy reading.
"""
        
        prompt_parts.append(analysis_request)
        
        return "\n".join(prompt_parts)
    
    def build_brand_specific_prompt(self, diagnostic_context: DiagnosticContext, brand_knowledge: Dict[str, Any]) -> str:
        """
        Build a brand-specific diagnostic prompt
        """
        base_prompt = self.build_dtc_analysis_prompt(diagnostic_context)
        
        brand_specific_info = f"""
\n=== BRAND-SPECIFIC INFORMATION ===

KNOWN ISSUES FOR {diagnostic_context.vehicle_info.make.upper() if diagnostic_context.vehicle_info.make else 'THIS VEHICLE'}:
"""
        
        if brand_knowledge.get('common_issues'):
            brand_specific_info += f"Common Issues:\n"
            for issue in brand_knowledge['common_issues']:
                brand_specific_info += f"- {issue}\n"
        
        if brand_knowledge.get('recalls'):
            brand_specific_info += f"\nKnown Recalls:\n"
            for recall in brand_knowledge['recalls']:
                brand_specific_info += f"- {recall}\n"
        
        if brand_knowledge.get('service_bulletins'):
            brand_specific_info += f"\nService Bulletins:\n"
            for bulletin in brand_knowledge['service_bulletins']:
                brand_specific_info += f"- {bulletin}\n"
        
        if brand_knowledge.get('special_procedures'):
            brand_specific_info += f"\nSpecial Procedures:\n"
            for procedure in brand_knowledge['special_procedures']:
                brand_specific_info += f"- {procedure}\n"
        
        brand_specific_info += "\nPlease consider these brand-specific factors in your analysis.\n"
        
        return base_prompt + brand_specific_info
    
    def build_cost_estimation_prompt(self, diagnostic_context: DiagnosticContext, repair_recommendations: List[str]) -> str:
        """
        Build a prompt for cost estimation
        """
        prompt = f"""You are an automotive cost estimation expert. Based on the following diagnostic information and repair recommendations, provide detailed cost estimates.

VEHICLE: {self._format_vehicle_info(diagnostic_context.vehicle_info)}

RECOMMENDED REPAIRS:
"""
        
        for i, repair in enumerate(repair_recommendations, 1):
            prompt += f"{i}. {repair}\n"
        
        prompt += """
PLEASE PROVIDE:

1. COST BREAKDOWN:
   - Parts cost (OEM vs Aftermarket options)
   - Labor cost (hours × shop rate)
   - Additional fees (shop supplies, disposal, etc.)
   - Total estimated cost range

2. COST OPTIMIZATION:
   - Money-saving alternatives
   - DIY vs Professional repair considerations
   - Timing recommendations (urgent vs can wait)

3. REGIONAL CONSIDERATIONS:
   - Typical labor rates in different regions
   - Parts availability factors
   - Warranty considerations

4. FINANCING OPTIONS:
   - Payment plan possibilities
   - Insurance coverage potential
   - Extended warranty applicability

Provide cost ranges (low-high) and explain factors that affect pricing.
"""
        
        return prompt
    
    def build_maintenance_prompt(self, diagnostic_context: DiagnosticContext) -> str:
        """
        Build a prompt for maintenance recommendations
        """
        prompt = f"""You are a preventive maintenance specialist. Based on the current diagnostic data, provide a comprehensive maintenance plan.

VEHICLE: {self._format_vehicle_info(diagnostic_context.vehicle_info)}

CURRENT ISSUES:
"""
        
        for dtc in diagnostic_context.dtcs:
            prompt += f"- {dtc.code}: {dtc.description}\n"
        
        prompt += """
PLEASE PROVIDE:

1. IMMEDIATE MAINTENANCE NEEDS:
   - Critical items requiring immediate attention
   - Safety-related maintenance

2. SCHEDULED MAINTENANCE PLAN:
   - Next 3 months
   - Next 6 months
   - Next 12 months

3. PREVENTIVE MEASURES:
   - How to prevent current issues from recurring
   - Early warning signs to monitor
   - Driving habit recommendations

4. MAINTENANCE SCHEDULE OPTIMIZATION:
   - Bundling opportunities to save costs
   - Seasonal considerations
   - Mileage-based vs time-based intervals

5. DIY MAINTENANCE OPPORTUNITIES:
   - Tasks the owner can perform
   - Required tools and skills
   - When to seek professional help

Focus on cost-effective maintenance that maximizes vehicle reliability and longevity.
"""
        
        return prompt
    
    def _format_vehicle_info(self, vehicle_info: VehicleContext) -> str:
        """Format vehicle information for prompts"""
        info_parts = []
        
        if vehicle_info.make:
            info_parts.append(f"Make: {vehicle_info.make}")
        if vehicle_info.model:
            info_parts.append(f"Model: {vehicle_info.model}")
        if vehicle_info.year:
            info_parts.append(f"Year: {vehicle_info.year}")
        if vehicle_info.vin:
            info_parts.append(f"VIN: {vehicle_info.vin}")
        if vehicle_info.engine_type:
            info_parts.append(f"Engine: {vehicle_info.engine_type}")
        if vehicle_info.mileage:
            info_parts.append(f"Mileage: {vehicle_info.mileage:,} miles")
        if vehicle_info.fuel_type:
            info_parts.append(f"Fuel Type: {vehicle_info.fuel_type}")
        
        return "\n".join(info_parts) if info_parts else "Vehicle information not available"
    
    def _format_dtc_info(self, dtcs: List[DTCInfo]) -> str:
        """Format DTC information for prompts"""
        if not dtcs:
            return "No DTCs detected"
        
        dtc_lines = []
        for dtc in dtcs:
            dtc_line = f"- {dtc.code}: {dtc.description}"
            if dtc.severity != "Unknown":
                dtc_line += f" (Severity: {dtc.severity})"
            if dtc.system != "Unknown System":
                dtc_line += f" [System: {dtc.system}]"
            dtc_lines.append(dtc_line)
        
        return "\n".join(dtc_lines)
    
    def _format_parameter_info(self, parameters: List[OBDParameter]) -> str:
        """Format parameter information for prompts"""
        if not parameters:
            return "No parameter data available"
        
        param_lines = []
        for param in parameters:
            param_line = f"- {param.name}: {param.value}"
            if param.unit:
                param_line += f" {param.unit}"
            param_lines.append(param_line)
        
        return "\n".join(param_lines)
