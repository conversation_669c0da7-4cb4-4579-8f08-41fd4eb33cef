"""
Pydantic models for API request/response schemas
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ConnectionType(str, Enum):
    """OBD connection types"""
    USB = "usb"
    BLUETOOTH = "bluetooth"
    CAN = "can"
    WIFI = "wifi"


class DiagnosticStatus(str, Enum):
    """Diagnostic session status"""
    IDLE = "idle"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    SCANNING = "scanning"
    ANALYZING = "analyzing"
    COMPLETE = "complete"
    ERROR = "error"


class DTCSeverity(str, Enum):
    """DTC severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"
    UNKNOWN = "unknown"


# Request Models
class ConnectionRequest(BaseModel):
    """OBD connection request"""
    connection_type: ConnectionType
    port: Optional[str] = None
    baudrate: Optional[int] = 38400
    timeout: Optional[float] = 30.0


class VehicleInfoRequest(BaseModel):
    """Vehicle information request"""
    make: Optional[str] = None
    model: Optional[str] = None
    year: Optional[int] = None
    vin: Optional[str] = None
    engine_type: Optional[str] = None
    mileage: Optional[int] = None
    fuel_type: Optional[str] = None


class DiagnosticRequest(BaseModel):
    """Diagnostic scan request"""
    include_dtcs: bool = True
    include_parameters: bool = True
    include_freeze_frame: bool = False
    parameter_pids: Optional[List[str]] = None
    symptoms: Optional[str] = None
    recent_repairs: Optional[str] = None
    driving_conditions: Optional[str] = None


class ClearDTCRequest(BaseModel):
    """Clear DTC request"""
    confirm: bool = Field(..., description="Confirmation required to clear DTCs")


class AIAnalysisRequest(BaseModel):
    """AI analysis request"""
    vehicle_info: Optional[VehicleInfoRequest] = None
    include_brand_specific: bool = True
    include_cost_estimate: bool = True
    include_maintenance_plan: bool = True


# Response Models
class DTCResponse(BaseModel):
    """DTC response model"""
    code: str
    description: str
    status: str
    severity: DTCSeverity
    system: str
    possible_causes: List[str] = []
    repair_hints: List[str] = []
    related_pids: List[str] = []
    freeze_frame_data: Optional[Dict[str, Any]] = None
    timestamp: datetime


class ParameterResponse(BaseModel):
    """OBD parameter response"""
    pid: str
    name: str
    value: Any
    unit: str
    timestamp: datetime
    normal_range: Optional[str] = None
    status: Optional[str] = None


class ConnectionResponse(BaseModel):
    """Connection status response"""
    connected: bool
    connection_type: Optional[ConnectionType] = None
    port: Optional[str] = None
    protocol: Optional[str] = None
    supported_commands: Optional[int] = None
    vehicle_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class DiagnosticScanResponse(BaseModel):
    """Diagnostic scan response"""
    status: DiagnosticStatus
    dtcs: List[DTCResponse] = []
    parameters: List[ParameterResponse] = []
    scan_duration: Optional[float] = None
    timestamp: datetime
    error_message: Optional[str] = None


class RepairRecommendation(BaseModel):
    """Repair recommendation model"""
    description: str
    priority: str
    estimated_cost_low: Optional[float] = None
    estimated_cost_high: Optional[float] = None
    estimated_time: Optional[str] = None
    parts_needed: List[str] = []
    labor_type: Optional[str] = None  # "diy", "basic", "advanced", "dealer"


class CostEstimate(BaseModel):
    """Cost estimation model"""
    total_low: float
    total_high: float
    parts_cost_low: float
    parts_cost_high: float
    labor_cost_low: float
    labor_cost_high: float
    breakdown: List[Dict[str, Any]] = []
    notes: Optional[str] = None


class MaintenanceItem(BaseModel):
    """Maintenance item model"""
    description: str
    priority: str
    due_mileage: Optional[int] = None
    due_date: Optional[datetime] = None
    estimated_cost: Optional[float] = None
    category: str  # "immediate", "upcoming", "overdue", "preventive"


class AIAnalysisResponse(BaseModel):
    """AI analysis response"""
    summary: str
    confidence_score: float
    priority_issues: List[Dict[str, Any]] = []
    repair_recommendations: List[RepairRecommendation] = []
    cost_estimates: Optional[CostEstimate] = None
    safety_warnings: List[str] = []
    preventive_measures: List[str] = []
    maintenance_plan: List[MaintenanceItem] = []
    brand_specific_notes: Optional[str] = None
    processing_time: Optional[float] = None
    model_used: Optional[str] = None


class HistoryEntry(BaseModel):
    """Diagnostic history entry"""
    id: str
    timestamp: datetime
    vehicle_info: Optional[VehicleInfoRequest] = None
    dtc_count: int
    dtcs: List[DTCResponse] = []
    ai_analysis: Optional[AIAnalysisResponse] = None
    notes: Optional[str] = None


class SystemStatus(BaseModel):
    """System status response"""
    obd_connected: bool
    can_connected: bool
    ai_available: bool
    database_connected: bool
    last_scan: Optional[datetime] = None
    active_sessions: int = 0
    system_health: str = "healthy"  # "healthy", "warning", "error"


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# Utility Models
class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(1, ge=1)
    size: int = Field(20, ge=1, le=100)


class FilterParams(BaseModel):
    """Filter parameters for history"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    vehicle_make: Optional[str] = None
    severity: Optional[DTCSeverity] = None
    has_dtcs: Optional[bool] = None


class BrandProfileResponse(BaseModel):
    """Brand profile information"""
    brand_name: str
    supported_models: List[str]
    common_issues: List[str]
    recalls: List[str]
    service_bulletins: List[str]
    special_procedures: List[str]
    diagnostic_tips: List[str]
    required_tools: List[str] = []


class ECUInfoResponse(BaseModel):
    """ECU information response"""
    ecu_id: str
    name: str
    software_version: Optional[str] = None
    hardware_version: Optional[str] = None
    supplier_id: Optional[str] = None
    serial_number: Optional[str] = None
    supported_services: List[str] = []


class CANScanResponse(BaseModel):
    """CAN bus scan response"""
    ecus_found: List[ECUInfoResponse]
    scan_duration: float
    timestamp: datetime
    total_ecus: int


class RealTimeData(BaseModel):
    """Real-time monitoring data"""
    parameters: List[ParameterResponse]
    timestamp: datetime
    session_id: str


class ExportRequest(BaseModel):
    """Data export request"""
    format: str = Field(..., regex="^(json|csv|pdf)$")
    include_history: bool = True
    include_analysis: bool = True
    date_range: Optional[Dict[str, datetime]] = None


class ImportRequest(BaseModel):
    """Data import request"""
    data_type: str = Field(..., regex="^(dtc_database|vehicle_profiles|maintenance_schedules)$")
    file_format: str = Field(..., regex="^(json|csv|xml)$")
    overwrite_existing: bool = False
