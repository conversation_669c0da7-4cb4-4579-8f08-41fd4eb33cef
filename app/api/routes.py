"""
FastAPI routes for OBD2 diagnostic system
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse

from .models import *
from ..obd_interface.obd_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, DTCResult, OBDParameter
from ..obd_interface.can_reader import CANReader, ECUInfo
from ..obd_interface.dtc_parser import DTCParser
from ..ai_engine.explain_dtc import DTCExplainer
from ..ai_engine.prompt_builder import DiagnosticContext, VehicleContext
from ..brand_profiles.toyota import ToyotaProfile
from ..brand_profiles.vag import VAGProfile
from ..brand_profiles.bmw import BMWProfile


logger = logging.getLogger(__name__)

# Global instances
obd_reader = OBDReader()
can_reader = CANReader()
dtc_parser = DTCParser()
dtc_explainer = DTCExplainer()

# Brand profiles
brand_profiles = {
    "toyota": ToyotaProfile(),
    "lexus": ToyotaProfile(),
    "volkswagen": VAGProfile(),
    "audi": VAGProfile(),
    "skoda": VAGProfile(),
    "seat": VAGProfile(),
    "bmw": BMWProfile(),
    "mini": BMWProfile()
}

# Create router
router = APIRouter()


# Connection Management
@router.post("/connect", response_model=ConnectionResponse)
async def connect_obd(request: ConnectionRequest):
    """
    Connect to OBD2 adapter
    """
    try:
        if request.connection_type == ConnectionType.USB or request.connection_type == ConnectionType.BLUETOOTH:
            # Configure OBD reader
            obd_reader.port = request.port
            obd_reader.baudrate = request.baudrate or 38400
            
            # Attempt connection
            success = await obd_reader.connect()
            
            if success:
                vehicle_info = await obd_reader.get_vehicle_info()
                return ConnectionResponse(
                    connected=True,
                    connection_type=request.connection_type,
                    port=obd_reader.connection.port_name() if obd_reader.connection else request.port,
                    protocol=vehicle_info.get("protocol"),
                    supported_commands=vehicle_info.get("supported_commands"),
                    vehicle_info=vehicle_info
                )
            else:
                return ConnectionResponse(
                    connected=False,
                    error_message="Failed to connect to OBD2 adapter"
                )
        
        elif request.connection_type == ConnectionType.CAN:
            # Configure CAN reader
            can_reader.interface = request.port or "can0"
            
            # Attempt connection
            success = await can_reader.connect()
            
            if success:
                return ConnectionResponse(
                    connected=True,
                    connection_type=request.connection_type,
                    port=can_reader.interface
                )
            else:
                return ConnectionResponse(
                    connected=False,
                    error_message="Failed to connect to CAN interface"
                )
        
        else:
            raise HTTPException(status_code=400, detail="Unsupported connection type")
            
    except Exception as e:
        logger.error(f"Connection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/disconnect")
async def disconnect_obd():
    """
    Disconnect from OBD2 adapter
    """
    try:
        await obd_reader.disconnect()
        await can_reader.disconnect()
        return {"message": "Disconnected successfully"}
    except Exception as e:
        logger.error(f"Disconnection error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection/status", response_model=ConnectionResponse)
async def get_connection_status():
    """
    Get current connection status
    """
    try:
        obd_connected = obd_reader.is_connected
        can_connected = can_reader.is_connected
        
        if obd_connected:
            vehicle_info = await obd_reader.get_vehicle_info()
            return ConnectionResponse(
                connected=True,
                connection_type=ConnectionType.USB,  # or BLUETOOTH
                port=obd_reader.connection.port_name() if obd_reader.connection else None,
                protocol=vehicle_info.get("protocol"),
                supported_commands=vehicle_info.get("supported_commands"),
                vehicle_info=vehicle_info
            )
        elif can_connected:
            return ConnectionResponse(
                connected=True,
                connection_type=ConnectionType.CAN,
                port=can_reader.interface
            )
        else:
            return ConnectionResponse(connected=False)
            
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/connection/ports")
async def list_available_ports():
    """
    List available serial ports
    """
    try:
        ports = OBDReader.list_available_ports()
        return {"ports": ports}
    except Exception as e:
        logger.error(f"Port listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Diagnostic Operations
@router.post("/scan", response_model=DiagnosticScanResponse)
async def perform_diagnostic_scan(request: DiagnosticRequest):
    """
    Perform comprehensive diagnostic scan
    """
    try:
        start_time = datetime.now()
        dtcs = []
        parameters = []
        
        # Check connection
        if not obd_reader.is_connected and not can_reader.is_connected:
            raise HTTPException(status_code=400, detail="No OBD connection available")
        
        # Read DTCs
        if request.include_dtcs:
            if obd_reader.is_connected:
                obd_dtcs = await obd_reader.read_dtcs()
                dtcs.extend([
                    DTCResponse(
                        code=dtc.code,
                        description=dtc.description,
                        status=dtc.status,
                        severity=DTCSeverity.UNKNOWN,  # Will be determined by parser
                        system="Unknown",
                        timestamp=dtc.timestamp
                    ) for dtc in obd_dtcs
                ])
            
            # Also try CAN if available
            if can_reader.is_connected:
                # Scan common ECUs for DTCs
                ecu_ids = [0x7E0, 0x7E1, 0x7E2, 0x7E3]  # Engine, Trans, ABS, Airbag
                for ecu_id in ecu_ids:
                    try:
                        can_dtcs = await can_reader.read_dtc_information(ecu_id)
                        for dtc_code in can_dtcs:
                            dtcs.append(DTCResponse(
                                code=dtc_code,
                                description=f"CAN DTC from ECU {ecu_id:03X}",
                                status="stored",
                                severity=DTCSeverity.UNKNOWN,
                                system="CAN",
                                timestamp=datetime.now()
                            ))
                    except:
                        continue  # Skip non-responsive ECUs
        
        # Read parameters
        if request.include_parameters and obd_reader.is_connected:
            if request.parameter_pids:
                obd_params = await obd_reader.read_multiple_parameters(request.parameter_pids)
            else:
                # Read common parameters
                common_pids = ["0x0C", "0x0D", "0x05", "0x0B", "0x04"]  # RPM, Speed, Coolant temp, MAP, Load
                obd_params = await obd_reader.read_multiple_parameters(common_pids)
            
            parameters.extend([
                ParameterResponse(
                    pid=param.pid,
                    name=param.name,
                    value=param.value,
                    unit=param.unit,
                    timestamp=param.timestamp
                ) for param in obd_params
            ])
        
        # Parse DTCs for additional information
        for dtc_response in dtcs:
            dtc_info = dtc_parser.parse_dtc(dtc_response.code)
            dtc_response.severity = DTCSeverity(dtc_info.severity.lower())
            dtc_response.system = dtc_info.system
            dtc_response.possible_causes = dtc_info.possible_causes
            dtc_response.repair_hints = dtc_info.repair_hints
            dtc_response.related_pids = dtc_info.related_pids
        
        scan_duration = (datetime.now() - start_time).total_seconds()
        
        return DiagnosticScanResponse(
            status=DiagnosticStatus.COMPLETE,
            dtcs=dtcs,
            parameters=parameters,
            scan_duration=scan_duration,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Diagnostic scan error: {e}")
        return DiagnosticScanResponse(
            status=DiagnosticStatus.ERROR,
            error_message=str(e),
            timestamp=datetime.now()
        )


@router.post("/dtcs/clear")
async def clear_dtcs(request: ClearDTCRequest):
    """
    Clear stored DTCs
    """
    try:
        if not request.confirm:
            raise HTTPException(status_code=400, detail="Confirmation required to clear DTCs")
        
        if not obd_reader.is_connected:
            raise HTTPException(status_code=400, detail="No OBD connection available")
        
        success = await obd_reader.clear_dtcs()
        
        if success:
            return {"message": "DTCs cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear DTCs")
            
    except Exception as e:
        logger.error(f"Clear DTCs error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# AI Analysis
@router.post("/analyze", response_model=AIAnalysisResponse)
async def analyze_diagnostics(
    request: AIAnalysisRequest,
    dtcs: List[str] = Query([], description="DTC codes to analyze"),
    background_tasks: BackgroundTasks = None
):
    """
    Perform AI-powered diagnostic analysis
    """
    try:
        if not dtcs:
            # Get current DTCs if none provided
            if obd_reader.is_connected:
                current_dtcs = await obd_reader.read_dtcs()
                dtcs = [dtc.code for dtc in current_dtcs]
        
        if not dtcs:
            raise HTTPException(status_code=400, detail="No DTCs to analyze")
        
        # Parse DTCs
        dtc_infos = [dtc_parser.parse_dtc(code) for code in dtcs]
        
        # Get current parameters if available
        parameters = []
        if obd_reader.is_connected:
            common_pids = ["0x0C", "0x0D", "0x05", "0x0B", "0x04"]
            parameters = await obd_reader.read_multiple_parameters(common_pids)
        
        # Build diagnostic context
        vehicle_context = VehicleContext()
        if request.vehicle_info:
            vehicle_context.make = request.vehicle_info.make
            vehicle_context.model = request.vehicle_info.model
            vehicle_context.year = request.vehicle_info.year
            vehicle_context.vin = request.vehicle_info.vin
            vehicle_context.engine_type = request.vehicle_info.engine_type
            vehicle_context.mileage = request.vehicle_info.mileage
            vehicle_context.fuel_type = request.vehicle_info.fuel_type
        
        diagnostic_context = DiagnosticContext(
            dtcs=dtc_infos,
            parameters=parameters,
            vehicle_info=vehicle_context
        )
        
        # Perform AI analysis
        if request.include_brand_specific and vehicle_context.make:
            brand_key = vehicle_context.make.lower()
            if brand_key in brand_profiles:
                brand_profile = brand_profiles[brand_key]
                brand_knowledge = brand_profile.get_brand_knowledge(
                    vehicle_context.model or "", 
                    vehicle_context.year
                )
                analysis = await dtc_explainer.explain_brand_specific(diagnostic_context, brand_knowledge)
            else:
                analysis = await dtc_explainer.explain_dtcs(diagnostic_context)
        else:
            analysis = await dtc_explainer.explain_dtcs(diagnostic_context)
        
        # Convert to response format
        repair_recommendations = [
            RepairRecommendation(
                description=rec["description"],
                priority=rec["priority"],
                estimated_time=rec.get("estimated_time"),
                parts_needed=rec.get("parts_needed", [])
            ) for rec in analysis.repair_recommendations
        ]
        
        # Get cost estimates if requested
        cost_estimates = None
        if request.include_cost_estimate:
            cost_data = await dtc_explainer.estimate_repair_costs(
                diagnostic_context, 
                [rec.description for rec in repair_recommendations]
            )
            cost_estimates = CostEstimate(
                total_low=cost_data.get("total_estimate_low", 0),
                total_high=cost_data.get("total_estimate_high", 0),
                parts_cost_low=0,  # Would need more detailed parsing
                parts_cost_high=0,
                labor_cost_low=0,
                labor_cost_high=0,
                breakdown=cost_data.get("breakdown", []),
                notes=cost_data.get("notes")
            )
        
        # Get maintenance plan if requested
        maintenance_plan = []
        if request.include_maintenance_plan:
            maintenance_data = await dtc_explainer.generate_maintenance_plan(diagnostic_context)
            for category, items in maintenance_data.items():
                if category != "notes":
                    for item in items:
                        maintenance_plan.append(MaintenanceItem(
                            description=item,
                            priority=category,
                            category=category
                        ))
        
        return AIAnalysisResponse(
            summary=analysis.summary,
            confidence_score=analysis.confidence_score,
            priority_issues=analysis.priority_issues,
            repair_recommendations=repair_recommendations,
            cost_estimates=cost_estimates,
            safety_warnings=analysis.safety_warnings,
            preventive_measures=analysis.preventive_measures,
            maintenance_plan=maintenance_plan,
            processing_time=analysis.ai_response.processing_time,
            model_used=analysis.ai_response.model_used
        )
        
    except Exception as e:
        logger.error(f"AI analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Brand Profiles
@router.get("/brands/{brand_name}", response_model=BrandProfileResponse)
async def get_brand_profile(brand_name: str, model: Optional[str] = None, year: Optional[int] = None):
    """
    Get brand-specific diagnostic information
    """
    try:
        brand_key = brand_name.lower()
        if brand_key not in brand_profiles:
            raise HTTPException(status_code=404, detail="Brand profile not found")
        
        profile = brand_profiles[brand_key]
        knowledge = profile.get_brand_knowledge(model or "", year)
        
        return BrandProfileResponse(
            brand_name=profile.brand_name,
            supported_models=getattr(profile, 'supported_models', []),
            common_issues=knowledge.get("common_issues", []),
            recalls=knowledge.get("recalls", []),
            service_bulletins=knowledge.get("service_bulletins", []),
            special_procedures=knowledge.get("special_procedures", []),
            diagnostic_tips=knowledge.get("diagnostic_tips", []),
            required_tools=knowledge.get("required_tools", [])
        )
        
    except Exception as e:
        logger.error(f"Brand profile error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/brands")
async def list_supported_brands():
    """
    List all supported vehicle brands
    """
    try:
        brands = []
        for brand_key, profile in brand_profiles.items():
            brands.append({
                "key": brand_key,
                "name": profile.brand_name,
                "supported_models": getattr(profile, 'supported_models', [])
            })
        
        return {"brands": brands}
        
    except Exception as e:
        logger.error(f"Brand listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# CAN Bus Operations
@router.post("/can/scan", response_model=CANScanResponse)
async def scan_can_bus():
    """
    Scan CAN bus for available ECUs
    """
    try:
        if not can_reader.is_connected:
            raise HTTPException(status_code=400, detail="CAN interface not connected")
        
        start_time = datetime.now()
        ecus = await can_reader.scan_ecus()
        scan_duration = (datetime.now() - start_time).total_seconds()
        
        ecu_responses = [
            ECUInfoResponse(
                ecu_id=f"{ecu.ecu_id:03X}",
                name=ecu.name,
                software_version=ecu.software_version,
                hardware_version=ecu.hardware_version,
                supplier_id=ecu.supplier_id,
                serial_number=ecu.serial_number
            ) for ecu in ecus
        ]
        
        return CANScanResponse(
            ecus_found=ecu_responses,
            scan_duration=scan_duration,
            timestamp=datetime.now(),
            total_ecus=len(ecus)
        )
        
    except Exception as e:
        logger.error(f"CAN scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# System Status
@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """
    Get overall system status
    """
    try:
        return SystemStatus(
            obd_connected=obd_reader.is_connected,
            can_connected=can_reader.is_connected,
            ai_available=dtc_explainer.openai_client is not None or dtc_explainer.local_llm is not None,
            database_connected=True,  # Would check actual database connection
            system_health="healthy"
        )
        
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
