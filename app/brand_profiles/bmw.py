"""
BMW/MINI specific diagnostic profiles and procedures
Based on open-source research and community knowledge
"""
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from ..obd_interface.dtc_parser import DTCInfo


@dataclass
class BMWSpecificPID:
    """BMW-specific PID definition"""
    pid: str
    name: str
    description: str
    unit: str
    ista_reference: Optional[str] = None


class BMWProfile:
    """
    BMW/MINI specific diagnostic profile
    """
    
    def __init__(self):
        self.brand_name = "BMW"
        self.supported_brands = ["BMW", "MINI"]
        self.supported_models = [
            # BMW
            "1 Series", "2 Series", "3 Series", "4 Series", "5 Series", 
            "6 Series", "7 Series", "8 Series", "X1", "X2", "X3", "X4", 
            "X5", "X6", "X7", "Z4", "i3", "i8",
            # MINI
            "Cooper", "Countryman", "Clubman", "Paceman"
        ]
        
        # BMW-specific PIDs and parameters
        self.specific_pids = {
            "0x01": BMWSpecificPID("0x01", "Valvetronic Position", "Valvetronic Motor Position", "steps", "ISTA/D"),
            "0x02": BMWSpecificPID("0x02", "VANOS Position", "VANOS Actuator Position", "°", "ISTA/D"),
            "0x03": BMWSpecificPID("0x03", "DME Temperature", "Engine Control Module Temp", "°C", "ISTA/D"),
            "0x04": BMWSpecificPID("0x04", "Fuel Rail Pressure", "High Pressure Fuel Rail", "bar", "ISTA/D"),
            "0x05": BMWSpecificPID("0x05", "Turbo Boost", "Turbocharger Boost Pressure", "mbar", "ISTA/D"),
            "0x06": BMWSpecificPID("0x06", "DPF Pressure", "DPF Differential Pressure", "mbar", "ISTA/D"),
            "0x07": BMWSpecificPID("0x07", "AdBlue Level", "DEF Tank Level", "%", "ISTA/D"),
            "0x08": BMWSpecificPID("0x08", "Battery SOC", "High Voltage Battery SOC", "%", "ISTA/D"),
            "0x09": BMWSpecificPID("0x09", "Transmission Temp", "Automatic Transmission Temp", "°C", "ISTA/D"),
            "0x0A": BMWSpecificPID("0x0A", "DSC Status", "Dynamic Stability Control", "status", "ISTA/D")
        }
    
    def get_common_issues(self, model: str, year: Optional[int] = None) -> List[str]:
        """
        Get common issues for specific BMW models
        """
        common_issues = []
        
        # N54/N55 engine issues (3 Series, 5 Series, X5, X6)
        if any(series in model.lower() for series in ["3 series", "5 series", "x5", "x6"]):
            if year and 2007 <= year <= 2016:
                common_issues.extend([
                    "High pressure fuel pump failure (N54/N55)",
                    "Turbocharger wastegate rattle",
                    "Carbon buildup on intake valves",
                    "Valvetronic motor failure",
                    "VANOS solenoid issues",
                    "Water pump failure (electric)",
                    "Thermostat housing leak"
                ])
        
        # N20/N26 engine issues (newer 3/4 Series, X1, X3)
        if year and year >= 2012:
            common_issues.extend([
                "Timing chain stretch (N20/N26)",
                "Turbocharger failure",
                "Oil filter housing gasket leak",
                "Coolant loss issues",
                "PCV valve failure"
            ])
        
        # Diesel engine issues
        if "d" in model.lower() or year and year >= 2009:
            common_issues.extend([
                "DPF clogging and regeneration issues",
                "EGR valve carbon buildup",
                "AdBlue system faults (Euro 6)",
                "Swirl flap failure",
                "Injector carbon deposits",
                "Turbocharger variable geometry issues"
            ])
        
        # Transmission issues
        common_issues.extend([
            "ZF 8-speed transmission mechatronic failure",
            "Transmission fluid leaks",
            "Shift solenoid issues",
            "Torque converter problems"
        ])
        
        # Electrical and comfort issues
        common_issues.extend([
            "iDrive system failures",
            "Window regulator failure",
            "Door lock actuator failure",
            "Xenon/LED headlight ballast failure",
            "CCC/CIC/NBT unit issues",
            "Parking brake actuator failure"
        ])
        
        # Model-specific issues
        if "3 series" in model.lower():
            if year and 2005 <= year <= 2013:  # E90/E92/E93
                common_issues.extend([
                    "HPFP failure (N54)",
                    "Wastegate rattle (N54)",
                    "Water pump failure",
                    "Electric steering pump failure"
                ])
        
        elif "5 series" in model.lower():
            if year and 2004 <= year <= 2010:  # E60/E61
                common_issues.extend([
                    "Air suspension compressor failure",
                    "iDrive controller failure",
                    "Valve stem seal leaks"
                ])
        
        elif "x5" in model.lower():
            common_issues.extend([
                "Transfer case actuator motor failure",
                "Air suspension issues",
                "Panoramic sunroof problems"
            ])
        
        return common_issues
    
    def get_recalls_and_tsbs(self, model: str, year: Optional[int] = None) -> Dict[str, List[str]]:
        """
        Get known recalls and Technical Service Bulletins
        """
        recalls_tsbs = {
            "recalls": [],
            "service_bulletins": []
        }
        
        # Engine-related recalls
        recalls_tsbs["recalls"].extend([
            "N54 High Pressure Fuel Pump replacement",
            "N20/N26 Timing chain replacement",
            "Valvetronic motor replacement",
            "Turbocharger replacement (various models)",
            "PCV valve replacement campaign"
        ])
        
        # Safety recalls
        recalls_tsbs["recalls"].extend([
            "Takata airbag inflator replacement",
            "Parking brake actuator replacement",
            "Power steering pump replacement",
            "Fuel pump flange replacement"
        ])
        
        # Service bulletins
        recalls_tsbs["service_bulletins"].extend([
            "SI B11 06 15: N54 HPFP diagnosis and replacement",
            "SI B11 14 16: N20 timing chain inspection procedure",
            "SI B11 03 17: Valvetronic adaptation procedure",
            "SI B13 01 18: DPF regeneration troubleshooting",
            "SI B61 02 19: iDrive system software update",
            "SI B34 03 20: Transmission adaptation procedure",
            "SI B11 05 21: Carbon cleaning procedure for direct injection"
        ])
        
        return recalls_tsbs
    
    def get_special_procedures(self, dtc_code: str) -> List[str]:
        """
        Get BMW-specific diagnostic procedures for DTCs
        """
        procedures = []
        
        # Valvetronic system codes
        if dtc_code in ['P1014', 'P1015', 'P1016']:
            procedures.extend([
                "Check Valvetronic motor operation with ISTA",
                "Perform Valvetronic adaptation",
                "Test eccentric shaft sensor",
                "Check for mechanical binding",
                "Verify power supply to motor",
                "Perform Valvetronic reset procedure"
            ])
        
        # VANOS system codes
        elif dtc_code in ['P1397', 'P1398', 'P1399']:
            procedures.extend([
                "Check VANOS solenoid operation",
                "Test camshaft position sensors",
                "Verify oil pressure to VANOS unit",
                "Check for timing chain stretch",
                "Perform VANOS adaptation with ISTA",
                "Inspect VANOS unit for wear"
            ])
        
        # High pressure fuel pump codes
        elif dtc_code in ['P0087', 'P0088', 'P1177']:
            procedures.extend([
                "Test HPFP pressure with ISTA",
                "Check HPFP cam follower",
                "Verify low pressure fuel supply",
                "Test fuel pressure regulator",
                "Check for fuel contamination",
                "Perform HPFP adaptation"
            ])
        
        # DPF related codes (diesel)
        elif dtc_code in ['P2002', 'P2003', 'P244A']:
            procedures.extend([
                "Check DPF differential pressure",
                "Perform forced DPF regeneration with ISTA",
                "Test exhaust temperature sensors",
                "Check AdBlue system operation",
                "Verify DPF pressure lines",
                "Inspect for exhaust leaks"
            ])
        
        # Transmission codes
        elif dtc_code.startswith('P07') or dtc_code.startswith('P17'):
            procedures.extend([
                "Check transmission fluid level and condition",
                "Perform transmission adaptation with ISTA",
                "Test mechatronic unit operation",
                "Check shift solenoids",
                "Verify transmission temperature",
                "Perform transmission reset procedure"
            ])
        
        return procedures
    
    def get_coding_procedures(self, module: str) -> List[str]:
        """
        Get BMW-specific coding procedures
        """
        coding_procedures = {
            "dme": [
                "Connect ISTA/D to vehicle",
                "Select vehicle identification",
                "Go to Service Functions → Engine",
                "Select coding/programming option",
                "Follow on-screen instructions",
                "Perform test drive after coding"
            ],
            "cas": [
                "Ensure all doors are closed",
                "Connect ISTA/P to vehicle",
                "Select CAS module",
                "Perform key programming procedure",
                "Test all key functions",
                "Check remote control operation"
            ],
            "kombi": [
                "Connect ISTA/D to vehicle",
                "Select Instrument Cluster",
                "Go to Service Functions",
                "Select coding option",
                "Configure vehicle options",
                "Verify display functions"
            ]
        }
        
        return coding_procedures.get(module, [])
    
    def get_maintenance_schedule(self, model: str, mileage: int) -> Dict[str, List[str]]:
        """
        Get BMW-specific maintenance recommendations
        """
        maintenance = {
            "immediate": [],
            "upcoming": [],
            "overdue": []
        }
        
        # BMW Condition Based Service intervals
        intervals = {
            10000: ["Engine oil and filter change", "Vehicle check"],
            20000: ["Cabin air filter", "Brake fluid check"],
            30000: ["Air filter", "Spark plugs", "Fuel filter"],
            50000: ["Brake fluid change", "Transmission service"],
            60000: ["Coolant change", "Thermostat inspection"],
            80000: ["Spark plugs (premium)", "Valvetronic adaptation"],
            100000: ["Water pump", "Timing chain inspection"],
            120000: ["Major service", "VANOS service"]
        }
        
        # Check what's due
        for interval, services in intervals.items():
            if mileage >= interval:
                if mileage - interval <= 2000:  # Within 2000 miles
                    maintenance["immediate"].extend(services)
                elif mileage - interval <= 10000:  # Within 10000 miles
                    maintenance["upcoming"].extend(services)
                else:
                    maintenance["overdue"].extend(services)
        
        # High-performance model specific maintenance
        if any(perf in model.lower() for perf in ["m3", "m5", "m6", "x5m", "x6m"]):
            if mileage >= 15000:
                maintenance["upcoming"].append("Differential service")
            if mileage >= 30000:
                maintenance["upcoming"].append("DCT transmission service")
        
        return maintenance
    
    def interpret_dtc(self, dtc_info: DTCInfo) -> Dict[str, Any]:
        """
        Provide BMW-specific interpretation of DTCs
        """
        interpretation = {
            "brand_specific_info": "",
            "common_causes": [],
            "repair_procedures": [],
            "parts_commonly_needed": [],
            "labor_time_estimate": "TBD",
            "ista_procedures": []
        }
        
        code = dtc_info.code
        
        # BMW-specific DTC interpretations
        if code in ["P1014", "P1015"]:  # Valvetronic
            interpretation.update({
                "brand_specific_info": "Valvetronic system fault - BMW's variable valve lift system",
                "common_causes": [
                    "Valvetronic motor failure",
                    "Eccentric shaft sensor issues",
                    "Mechanical binding in system",
                    "Carbon buildup on valves"
                ],
                "repair_procedures": self.get_special_procedures(code),
                "parts_commonly_needed": ["Valvetronic motor", "Eccentric shaft sensor", "Valvetronic unit"],
                "labor_time_estimate": "3-6 hours",
                "ista_procedures": ["Valvetronic adaptation", "Motor position learning"]
            })
        
        elif code == "P0087":  # HPFP
            interpretation.update({
                "brand_specific_info": "High Pressure Fuel Pump issue - common on N54/N55 engines",
                "common_causes": [
                    "HPFP mechanical failure",
                    "Cam follower wear",
                    "Fuel contamination",
                    "Low pressure fuel supply issues"
                ],
                "repair_procedures": self.get_special_procedures(code),
                "parts_commonly_needed": ["High pressure fuel pump", "Cam follower", "Fuel filter"],
                "labor_time_estimate": "4-6 hours",
                "ista_procedures": ["HPFP pressure test", "Fuel system adaptation"]
            })
        
        elif code.startswith("P07"):  # Transmission
            interpretation.update({
                "brand_specific_info": "ZF transmission fault - requires ISTA for proper diagnosis",
                "common_causes": [
                    "Mechatronic unit failure",
                    "Transmission fluid issues",
                    "Solenoid valve problems",
                    "Torque converter issues"
                ],
                "repair_procedures": self.get_special_procedures(code),
                "parts_commonly_needed": ["Mechatronic unit", "Transmission fluid", "Solenoids"],
                "labor_time_estimate": "4-8 hours",
                "ista_procedures": ["Transmission adaptation", "Shift point learning"]
            })
        
        return interpretation
    
    def get_brand_knowledge(self, model: str, year: Optional[int] = None) -> Dict[str, Any]:
        """
        Get comprehensive BMW brand knowledge
        """
        return {
            "common_issues": self.get_common_issues(model, year),
            "recalls": self.get_recalls_and_tsbs(model, year)["recalls"],
            "service_bulletins": self.get_recalls_and_tsbs(model, year)["service_bulletins"],
            "special_procedures": [
                "Always use ISTA/D or ISTA/P for diagnosis",
                "Perform adaptations after repairs",
                "Use BMW-approved fluids and parts",
                "Register battery after replacement",
                "Code modules after replacement"
            ],
            "diagnostic_tips": [
                "Many BMW systems require coding/adaptation",
                "Valvetronic system is unique to BMW",
                "HPFP issues common on N54/N55 engines",
                "Timing chain issues on N20/N26 engines",
                "iDrive system integration affects many functions"
            ],
            "required_tools": [
                "ISTA/D diagnostic software",
                "ISTA/P programming software",
                "BMW timing tools",
                "Valvetronic service tools",
                "High pressure fuel system tools"
            ]
        }
