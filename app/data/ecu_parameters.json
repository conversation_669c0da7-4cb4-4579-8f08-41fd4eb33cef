{"standard_pids": {"0x00": {"name": "PIDs supported [01 - 20]", "description": "Bit encoded list of supported PIDs", "unit": "bitmap", "formula": "raw_value", "normal_range": "varies"}, "0x01": {"name": "Monitor status since DTCs cleared", "description": "Status of emission-related monitoring systems", "unit": "bitmap", "formula": "raw_value", "normal_range": "varies"}, "0x02": {"name": "Freeze DTC", "description": "DTC that caused freeze frame to be stored", "unit": "code", "formula": "raw_value", "normal_range": "varies"}, "0x03": {"name": "Fuel system status", "description": "Status of fuel system", "unit": "status", "formula": "raw_value", "normal_range": "1-5"}, "0x04": {"name": "Calculated engine load", "description": "Current engine load calculated by ECU", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x05": {"name": "Engine coolant temperature", "description": "Temperature of engine coolant", "unit": "°C", "formula": "A - 40", "normal_range": "80-105"}, "0x06": {"name": "Short term fuel trim—Bank 1", "description": "Short term fuel adjustment for bank 1", "unit": "%", "formula": "(A - 128) * 100 / 128", "normal_range": "-10 to +10"}, "0x07": {"name": "Long term fuel trim—Bank 1", "description": "Long term fuel adjustment for bank 1", "unit": "%", "formula": "(A - 128) * 100 / 128", "normal_range": "-10 to +10"}, "0x08": {"name": "Short term fuel trim—Bank 2", "description": "Short term fuel adjustment for bank 2", "unit": "%", "formula": "(A - 128) * 100 / 128", "normal_range": "-10 to +10"}, "0x09": {"name": "Long term fuel trim—Bank 2", "description": "Long term fuel adjustment for bank 2", "unit": "%", "formula": "(A - 128) * 100 / 128", "normal_range": "-10 to +10"}, "0x0A": {"name": "Fuel pressure", "description": "Fuel rail pressure", "unit": "kPa", "formula": "A * 3", "normal_range": "200-400"}, "0x0B": {"name": "Intake manifold absolute pressure", "description": "Absolute pressure in intake manifold", "unit": "kPa", "formula": "A", "normal_range": "20-100"}, "0x0C": {"name": "Engine RPM", "description": "Engine revolutions per minute", "unit": "rpm", "formula": "((A * 256) + B) / 4", "normal_range": "600-6000"}, "0x0D": {"name": "Vehicle speed", "description": "Current vehicle speed", "unit": "km/h", "formula": "A", "normal_range": "0-255"}, "0x0E": {"name": "Timing advance", "description": "Ignition timing advance", "unit": "° before TDC", "formula": "A / 2 - 64", "normal_range": "5-35"}, "0x0F": {"name": "Intake air temperature", "description": "Temperature of intake air", "unit": "°C", "formula": "A - 40", "normal_range": "10-60"}, "0x10": {"name": "MAF air flow rate", "description": "Mass air flow sensor reading", "unit": "g/s", "formula": "((A * 256) + B) / 100", "normal_range": "2-400"}, "0x11": {"name": "Throttle position", "description": "Throttle position sensor reading", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x12": {"name": "Commanded secondary air status", "description": "Status of secondary air injection", "unit": "status", "formula": "raw_value", "normal_range": "varies"}, "0x13": {"name": "Oxygen sensors present", "description": "Which oxygen sensors are present", "unit": "bitmap", "formula": "raw_value", "normal_range": "varies"}, "0x14": {"name": "Oxygen Sensor 1", "description": "Bank 1, Sensor 1 voltage and fuel trim", "unit": "V, %", "formula": "A / 200, (B - 128) * 100 / 128", "normal_range": "0.1-0.9V, -25 to +25%"}, "0x15": {"name": "Oxygen Sensor 2", "description": "Bank 1, Sensor 2 voltage and fuel trim", "unit": "V, %", "formula": "A / 200, (B - 128) * 100 / 128", "normal_range": "0.1-0.9V, -25 to +25%"}, "0x1C": {"name": "OBD standards", "description": "OBD standards this vehicle conforms to", "unit": "code", "formula": "raw_value", "normal_range": "varies"}, "0x1F": {"name": "Run time since engine start", "description": "Time since engine was started", "unit": "seconds", "formula": "(A * 256) + B", "normal_range": "0-65535"}, "0x21": {"name": "Distance traveled with malfunction indicator lamp on", "description": "Distance with MIL on", "unit": "km", "formula": "(A * 256) + B", "normal_range": "0-65535"}, "0x22": {"name": "Fuel Rail Pressure", "description": "Fuel rail pressure relative to manifold vacuum", "unit": "kPa", "formula": "((A * 256) + B) * 0.079", "normal_range": "varies"}, "0x23": {"name": "Fuel Rail Gauge Pressure", "description": "Fuel rail gauge pressure", "unit": "kPa", "formula": "((A * 256) + B) * 10", "normal_range": "varies"}, "0x2F": {"name": "Fuel Tank Level Input", "description": "Fuel tank level input", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x30": {"name": "Warm-ups since codes cleared", "description": "Number of warm-up cycles since DTCs cleared", "unit": "count", "formula": "A", "normal_range": "0-255"}, "0x31": {"name": "Distance traveled since codes cleared", "description": "Distance traveled since DTCs cleared", "unit": "km", "formula": "(A * 256) + B", "normal_range": "0-65535"}, "0x33": {"name": "Absolute Barometric Pressure", "description": "Absolute barometric pressure", "unit": "kPa", "formula": "A", "normal_range": "80-110"}, "0x42": {"name": "Control module voltage", "description": "ECU supply voltage", "unit": "V", "formula": "((A * 256) + B) / 1000", "normal_range": "11-15"}, "0x43": {"name": "Absolute load value", "description": "Absolute load value", "unit": "%", "formula": "((A * 256) + B) * 100 / 255", "normal_range": "0-25700"}, "0x44": {"name": "Fuel–Air commanded equivalence ratio", "description": "Commanded air-fuel ratio", "unit": "ratio", "formula": "((A * 256) + B) / 32768", "normal_range": "0.5-1.5"}, "0x45": {"name": "Relative throttle position", "description": "Relative throttle position", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x46": {"name": "Ambient air temperature", "description": "Ambient air temperature", "unit": "°C", "formula": "A - 40", "normal_range": "-40 to +85"}, "0x47": {"name": "Absolute throttle position B", "description": "Absolute throttle position B", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x49": {"name": "Accelerator pedal position D", "description": "Accelerator pedal position D", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x4A": {"name": "Accelerator pedal position E", "description": "Accelerator pedal position E", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x4C": {"name": "Commanded throttle actuator", "description": "Commanded throttle actuator position", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x52": {"name": "Ethanol fuel %", "description": "Ethanol fuel percentage", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x5A": {"name": "Relative accelerator pedal position", "description": "Relative accelerator pedal position", "unit": "%", "formula": "A * 100 / 255", "normal_range": "0-100"}, "0x5C": {"name": "Engine oil temperature", "description": "Engine oil temperature", "unit": "°C", "formula": "A - 40", "normal_range": "80-120"}}, "manufacturer_specific": {"toyota": {"hybrid_battery_soc": {"name": "Hybrid Battery State of Charge", "description": "HV battery charge level", "unit": "%", "normal_range": "40-80"}, "hybrid_battery_voltage": {"name": "Hybrid Battery Voltage", "description": "High voltage battery voltage", "unit": "V", "normal_range": "200-300"}, "mg1_rpm": {"name": "Motor Generator 1 RPM", "description": "MG1 motor speed", "unit": "rpm", "normal_range": "-10000 to +10000"}, "mg2_rpm": {"name": "Motor Generator 2 RPM", "description": "MG2 motor speed", "unit": "rpm", "normal_range": "-10000 to +10000"}}, "vag": {"boost_pressure": {"name": "Turbo Boost Pressure", "description": "Turbocharger boost pressure", "unit": "mbar", "normal_range": "0-2000"}, "dpf_pressure": {"name": "DPF Differential Pressure", "description": "Diesel particulate filter pressure difference", "unit": "mbar", "normal_range": "0-100"}, "adblue_level": {"name": "AdBlue Tank Level", "description": "DEF tank level", "unit": "%", "normal_range": "10-100"}}, "bmw": {"valvetronic_position": {"name": "Valvetronic Position", "description": "Valvetronic motor position", "unit": "steps", "normal_range": "0-1000"}, "vanos_position": {"name": "VANOS Position", "description": "Variable valve timing position", "unit": "degrees", "normal_range": "-30 to +30"}, "hpfp_pressure": {"name": "High Pressure Fuel Pump Pressure", "description": "Direct injection fuel rail pressure", "unit": "bar", "normal_range": "50-200"}}}}