"""
Database seed data - populate initial DTC codes and other reference data
"""
import json
import logging
from typing import List, Dict, Any
from pathlib import Path

from .database import db_manager, dtc_repository
from .models import DTCCode, ECUParameter, BrandProfile


logger = logging.getLogger(__name__)


def load_dtc_codes_from_json() -> List[Dict[str, Any]]:
    """Load DTC codes from the original JSON file"""
    json_file_path = Path("app/data/dtc_codes.json")
    
    if not json_file_path.exists():
        logger.warning(f"DTC codes JSON file not found: {json_file_path}")
        return []
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            dtc_data = json.load(f)
        
        dtc_list = []
        for code, info in dtc_data.items():
            dtc_record = {
                'code': code,
                'description': info.get('description', ''),
                'category': info.get('category', 'Unknown'),
                'severity': info.get('severity', 'unknown'),
                'system': info.get('system', 'Unknown'),
                'possible_causes': info.get('possible_causes', []),
                'repair_hints': info.get('repair_hints', []),
                'related_pids': info.get('related_pids', []),
                'freeze_frame_required': info.get('freeze_frame_required', False),
                'obd2_standard': not code.startswith(('DF', 'VAG', 'BMW')),  # Manufacturer specific detection
                'brand_specific_info': {}
            }
            dtc_list.append(dtc_record)
        
        logger.info(f"Loaded {len(dtc_list)} DTC codes from JSON")
        return dtc_list
        
    except Exception as e:
        logger.error(f"Error loading DTC codes from JSON: {e}")
        return []


def get_additional_dtc_codes() -> List[Dict[str, Any]]:
    """Get additional DTC codes not in the original JSON"""
    additional_dtcs = [
        # More Powertrain codes
        {
            'code': 'P0172',
            'description': 'System Too Rich (Bank 1)',
            'category': 'Powertrain',
            'severity': 'medium',
            'system': 'Fuel and Air Metering',
            'possible_causes': [
                'Faulty fuel injector',
                'Fuel pressure regulator malfunction',
                'Faulty oxygen sensor',
                'Dirty air filter',
                'Faulty MAF sensor'
            ],
            'repair_hints': [
                'Test fuel injectors',
                'Check fuel pressure',
                'Test oxygen sensors',
                'Replace air filter',
                'Clean MAF sensor'
            ],
            'related_pids': ['0x06', '0x07', '0x10', '0x14'],
            'freeze_frame_required': True,
            'obd2_standard': True,
            'brand_specific_info': {}
        },
        
        # Body codes
        {
            'code': 'B1000',
            'description': 'ECU Internal Fault',
            'category': 'Body',
            'severity': 'high',
            'system': 'Body Control Module',
            'possible_causes': [
                'ECU hardware failure',
                'Software corruption',
                'Power supply issues',
                'Internal memory fault'
            ],
            'repair_hints': [
                'Check ECU power supply',
                'Attempt ECU reset',
                'Update ECU software',
                'Replace ECU if necessary'
            ],
            'related_pids': [],
            'freeze_frame_required': False,
            'obd2_standard': True,
            'brand_specific_info': {}
        },
        
        # Chassis codes
        {
            'code': 'C1000',
            'description': 'ABS System Fault',
            'category': 'Chassis',
            'severity': 'high',
            'system': 'ABS System',
            'possible_causes': [
                'ABS pump motor fault',
                'Hydraulic unit malfunction',
                'ECU communication error',
                'Power supply fault'
            ],
            'repair_hints': [
                'Test ABS pump motor',
                'Check hydraulic unit',
                'Verify ECU communication',
                'Check power supply to ABS system'
            ],
            'related_pids': [],
            'freeze_frame_required': False,
            'obd2_standard': True,
            'brand_specific_info': {}
        },
        
        # Network codes
        {
            'code': 'U0101',
            'description': 'Lost Communication With TCM',
            'category': 'Network',
            'severity': 'high',
            'system': 'Network/Communication',
            'possible_causes': [
                'Faulty TCM',
                'CAN bus wiring fault',
                'Connector issues',
                'Power supply problems'
            ],
            'repair_hints': [
                'Check TCM power and ground',
                'Test CAN bus wiring',
                'Inspect connectors',
                'Verify TCM operation'
            ],
            'related_pids': [],
            'freeze_frame_required': False,
            'obd2_standard': True,
            'brand_specific_info': {}
        },
        
        # Manufacturer specific codes
        {
            'code': 'DF001',
            'description': 'Injection System Fault (Renault)',
            'category': 'Powertrain',
            'severity': 'high',
            'system': 'Fuel Injection',
            'possible_causes': [
                'Faulty fuel injector',
                'Fuel pressure regulator issue',
                'ECU communication fault',
                'Wiring harness problem'
            ],
            'repair_hints': [
                'Check fuel injector resistance',
                'Test fuel pressure',
                'Verify ECU communication',
                'Inspect wiring harness'
            ],
            'related_pids': [],
            'freeze_frame_required': True,
            'obd2_standard': False,
            'brand_specific_info': {
                'renault': {
                    'ecu_address': '0x10',
                    'special_procedures': [
                        'Use Renault CLIP tool',
                        'Perform injector coding after replacement'
                    ]
                }
            }
        }
    ]
    
    return additional_dtcs


def get_ecu_parameters() -> List[Dict[str, Any]]:
    """Get ECU parameter definitions"""
    parameters = [
        {
            'pid': '0x0C',
            'name': 'Engine RPM',
            'description': 'Engine revolutions per minute',
            'unit': 'rpm',
            'formula': '((A*256)+B)/4',
            'min_value': 0,
            'max_value': 8000,
            'normal_range_min': 600,
            'normal_range_max': 6000,
            'mode': 1,
            'response_length': 2,
            'update_frequency': 'fast',
            'brand_specific': False,
            'supported_brands': ['all']
        },
        {
            'pid': '0x0D',
            'name': 'Vehicle Speed',
            'description': 'Current vehicle speed',
            'unit': 'km/h',
            'formula': 'A',
            'min_value': 0,
            'max_value': 255,
            'normal_range_min': 0,
            'normal_range_max': 200,
            'mode': 1,
            'response_length': 1,
            'update_frequency': 'fast',
            'brand_specific': False,
            'supported_brands': ['all']
        },
        {
            'pid': '0x05',
            'name': 'Engine Coolant Temperature',
            'description': 'Temperature of engine coolant',
            'unit': '°C',
            'formula': 'A-40',
            'min_value': -40,
            'max_value': 215,
            'normal_range_min': 80,
            'normal_range_max': 105,
            'mode': 1,
            'response_length': 1,
            'update_frequency': 'medium',
            'brand_specific': False,
            'supported_brands': ['all']
        },
        {
            'pid': '0x11',
            'name': 'Throttle Position',
            'description': 'Throttle position sensor reading',
            'unit': '%',
            'formula': 'A*100/255',
            'min_value': 0,
            'max_value': 100,
            'normal_range_min': 0,
            'normal_range_max': 100,
            'mode': 1,
            'response_length': 1,
            'update_frequency': 'fast',
            'brand_specific': False,
            'supported_brands': ['all']
        },
        {
            'pid': '0x10',
            'name': 'MAF Air Flow Rate',
            'description': 'Mass air flow sensor reading',
            'unit': 'g/s',
            'formula': '((A*256)+B)/100',
            'min_value': 0,
            'max_value': 655.35,
            'normal_range_min': 2,
            'normal_range_max': 400,
            'mode': 1,
            'response_length': 2,
            'update_frequency': 'fast',
            'brand_specific': False,
            'supported_brands': ['all']
        }
    ]
    
    return parameters


def get_brand_profiles() -> List[Dict[str, Any]]:
    """Get brand profile definitions"""
    profiles = [
        {
            'brand_name': 'Toyota',
            'full_name': 'Toyota Motor Corporation',
            'country_of_origin': 'Japan',
            'parent_company': 'Toyota Motor Corporation',
            'supported_models': [
                'Prius', 'Camry', 'Corolla', 'RAV4', 'Highlander',
                'Sienna', 'Tacoma', 'Tundra', '4Runner', 'Sequoia'
            ],
            'year_range_start': 1996,
            'year_range_end': 2024,
            'supported_protocols': ['OBD2', 'ISO 14230', 'CAN'],
            'special_procedures': [
                'Hybrid system diagnostics',
                'VVT-i system analysis',
                'Techstream compatibility'
            ],
            'common_issues': [
                'Oil consumption (2007-2011)',
                'Hybrid battery degradation',
                'VVT-i actuator problems'
            ],
            'recalls_tsbs': [
                'Oil consumption inspection procedure',
                'Hybrid battery inspection',
                'EGR system cleaning procedure'
            ],
            'required_tools': ['Techstream', 'OBD2 scanner'],
            'diagnostic_software': ['Techstream', 'TIS'],
            'active': True
        },
        
        {
            'brand_name': 'Volkswagen',
            'full_name': 'Volkswagen AG',
            'country_of_origin': 'Germany',
            'parent_company': 'Volkswagen Group',
            'supported_models': [
                'Golf', 'Jetta', 'Passat', 'Tiguan', 'Atlas', 'Arteon'
            ],
            'year_range_start': 1996,
            'year_range_end': 2024,
            'supported_protocols': ['OBD2', 'UDS', 'KWP2000', 'CAN'],
            'special_procedures': [
                'DSG transmission diagnostics',
                'DPF regeneration procedures',
                'VCDS adaptations'
            ],
            'common_issues': [
                'Carbon buildup (TSI engines)',
                'DSG mechatronic failures',
                'DPF clogging (TDI engines)'
            ],
            'recalls_tsbs': [
                'Timing chain tensioner replacement',
                'DSG transmission software update',
                'DPF regeneration troubleshooting'
            ],
            'required_tools': ['VCDS', 'VAG-COM'],
            'diagnostic_software': ['VCDS', 'ODIS'],
            'active': True
        },
        
        {
            'brand_name': 'BMW',
            'full_name': 'Bayerische Motoren Werke AG',
            'country_of_origin': 'Germany',
            'parent_company': 'BMW Group',
            'supported_models': [
                '3 Series', '5 Series', '7 Series', 'X3', 'X5', 'X7'
            ],
            'year_range_start': 1996,
            'year_range_end': 2024,
            'supported_protocols': ['OBD2', 'UDS', 'EDIABAS', 'CAN'],
            'special_procedures': [
                'Valvetronic system diagnostics',
                'VANOS system analysis',
                'ISTA programming'
            ],
            'common_issues': [
                'HPFP failure (N54/N55)',
                'Valvetronic motor failure',
                'Water pump failure'
            ],
            'recalls_tsbs': [
                'N54 HPFP replacement',
                'Valvetronic motor replacement',
                'Timing chain inspection'
            ],
            'required_tools': ['ISTA/D', 'ISTA/P'],
            'diagnostic_software': ['ISTA', 'INPA'],
            'active': True
        }
    ]
    
    return profiles


def seed_database():
    """Seed database with initial data"""
    try:
        logger.info("Starting database seeding...")
        
        # Load and insert DTC codes
        dtc_codes = load_dtc_codes_from_json()
        additional_dtcs = get_additional_dtc_codes()
        all_dtcs = dtc_codes + additional_dtcs
        
        if all_dtcs:
            # Check if DTCs already exist
            existing_count = 0
            with db_manager.get_session() as session:
                existing_count = session.query(DTCCode).count()
            
            if existing_count == 0:
                inserted_count = dtc_repository.bulk_insert_dtcs(all_dtcs)
                logger.info(f"Inserted {inserted_count} DTC codes")
            else:
                logger.info(f"DTC codes already exist ({existing_count} records)")
        
        # Insert ECU parameters
        parameters = get_ecu_parameters()
        with db_manager.get_session() as session:
            existing_params = session.query(ECUParameter).count()
            if existing_params == 0:
                param_objects = [ECUParameter(**param) for param in parameters]
                session.bulk_save_objects(param_objects)
                logger.info(f"Inserted {len(parameters)} ECU parameters")
            else:
                logger.info(f"ECU parameters already exist ({existing_params} records)")
        
        # Insert brand profiles
        profiles = get_brand_profiles()
        with db_manager.get_session() as session:
            existing_profiles = session.query(BrandProfile).count()
            if existing_profiles == 0:
                profile_objects = [BrandProfile(**profile) for profile in profiles]
                session.bulk_save_objects(profile_objects)
                logger.info(f"Inserted {len(profiles)} brand profiles")
            else:
                logger.info(f"Brand profiles already exist ({existing_profiles} records)")
        
        logger.info("Database seeding completed successfully")
        
    except Exception as e:
        logger.error(f"Database seeding failed: {e}")
        raise


if __name__ == "__main__":
    # Run seeding if called directly
    from .database import init_database
    
    init_database()
    seed_database()
