"""
CAN Bus Reader implementation for UDS/KWP2000 protocols
Supports direct ECU communication via python-can
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

import can
from can import Message
import isotp
from isotp import protocol, address, errors

from ..config import settings, CANConfig


logger = logging.getLogger(__name__)


@dataclass
class UDSResponse:
    """UDS response data structure"""
    service_id: int
    data: bytes
    success: bool
    error_code: Optional[int] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ECUInfo:
    """ECU information structure"""
    ecu_id: int
    name: str
    software_version: Optional[str] = None
    hardware_version: Optional[str] = None
    supplier_id: Optional[str] = None
    serial_number: Optional[str] = None


class CANReader:
    """
    CAN Bus reader for UDS/KWP2000 communication
    """
    
    def __init__(self, interface: str = "can0", bitrate: int = 500000):
        self.interface = interface or settings.can_interface
        self.bitrate = bitrate
        self.bus: Optional[can.Bus] = None
        self.is_connected = False
        self.isotp_stacks: Dict[int, isotp.protocol.IsoTpProtocol] = {}
        
    async def connect(self) -> bool:
        """
        Initialize CAN bus connection
        """
        try:
            self.bus = can.Bus(
                interface='socketcan',
                channel=self.interface,
                bitrate=self.bitrate
            )
            self.is_connected = True
            logger.info(f"CAN bus connected on {self.interface} at {self.bitrate} bps")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to CAN bus: {e}")
            return False
    
    async def disconnect(self):
        """
        Close CAN bus connection
        """
        if self.bus:
            self.bus.shutdown()
            self.is_connected = False
            logger.info("CAN bus connection closed")
    
    def setup_isotp_stack(self, tx_id: int, rx_id: int) -> isotp.protocol.IsoTpProtocol:
        """
        Setup ISO-TP stack for specific ECU communication
        """
        addr = isotp.address.Normal_29bits(tx_id=tx_id, rx_id=rx_id)
        stack = isotp.protocol.IsoTpProtocol(addr)
        self.isotp_stacks[tx_id] = stack
        return stack
    
    async def send_uds_request(self, ecu_id: int, service_id: int, data: bytes = b'') -> UDSResponse:
        """
        Send UDS request to specific ECU
        """
        if not self.is_connected:
            raise ConnectionError("CAN bus not connected")
        
        try:
            # Setup ISO-TP if not exists
            if ecu_id not in self.isotp_stacks:
                rx_id = ecu_id + 8  # Standard offset for response
                self.setup_isotp_stack(ecu_id, rx_id)
            
            stack = self.isotp_stacks[ecu_id]
            
            # Prepare UDS request
            request_data = bytes([service_id]) + data
            
            # Send request
            stack.send(request_data)
            
            # Process ISO-TP protocol
            while stack.transmit_data():
                msg = stack.get_tx_data()
                can_msg = Message(arbitration_id=ecu_id, data=msg, is_extended_id=False)
                self.bus.send(can_msg)
            
            # Wait for response
            timeout = 5.0  # 5 seconds timeout
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < timeout:
                msg = self.bus.recv(timeout=0.1)
                if msg and msg.arbitration_id == ecu_id + 8:
                    stack.recv(msg.data)
                    
                    if stack.available():
                        response_data = stack.recv()
                        
                        if len(response_data) > 0:
                            response_service = response_data[0]
                            
                            # Check for positive response
                            if response_service == service_id + 0x40:
                                return UDSResponse(
                                    service_id=service_id,
                                    data=response_data[1:],
                                    success=True
                                )
                            # Check for negative response
                            elif response_service == 0x7F:
                                error_code = response_data[2] if len(response_data) > 2 else None
                                return UDSResponse(
                                    service_id=service_id,
                                    data=response_data,
                                    success=False,
                                    error_code=error_code
                                )
            
            # Timeout
            return UDSResponse(
                service_id=service_id,
                data=b'',
                success=False,
                error_code=0x78  # Request correctly received but response pending
            )
            
        except Exception as e:
            logger.error(f"Error sending UDS request to ECU {ecu_id:03X}: {e}")
            return UDSResponse(
                service_id=service_id,
                data=b'',
                success=False
            )
    
    async def read_dtc_information(self, ecu_id: int, dtc_type: int = 0x02) -> List[str]:
        """
        Read DTC information using UDS service 0x19
        """
        dtcs = []
        
        try:
            # Service 0x19 - Read DTC Information
            response = await self.send_uds_request(ecu_id, 0x19, bytes([dtc_type]))
            
            if response.success and len(response.data) >= 2:
                # Parse DTC data
                dtc_count = response.data[1]
                dtc_data = response.data[2:]
                
                # Each DTC is 3 bytes
                for i in range(0, len(dtc_data), 3):
                    if i + 2 < len(dtc_data):
                        dtc_bytes = dtc_data[i:i+3]
                        dtc_code = self._parse_dtc_bytes(dtc_bytes)
                        if dtc_code:
                            dtcs.append(dtc_code)
            
        except Exception as e:
            logger.error(f"Error reading DTC information from ECU {ecu_id:03X}: {e}")
        
        return dtcs
    
    def _parse_dtc_bytes(self, dtc_bytes: bytes) -> Optional[str]:
        """
        Parse 3-byte DTC format to standard DTC string
        """
        if len(dtc_bytes) != 3:
            return None
        
        try:
            # Extract DTC components
            first_byte = dtc_bytes[0]
            second_byte = dtc_bytes[1]
            third_byte = dtc_bytes[2]
            
            # Determine DTC prefix
            prefix_map = {0: 'P', 1: 'P', 2: 'P', 3: 'P',
                         4: 'B', 5: 'B', 6: 'B', 7: 'B',
                         8: 'C', 9: 'C', 10: 'C', 11: 'C',
                         12: 'U', 13: 'U', 14: 'U', 15: 'U'}
            
            prefix = prefix_map.get((first_byte >> 6) & 0x03, 'P')
            
            # Extract numeric part
            num1 = (first_byte >> 4) & 0x03
            num2 = first_byte & 0x0F
            num3 = (second_byte >> 4) & 0x0F
            num4 = second_byte & 0x0F
            num5 = (third_byte >> 4) & 0x0F
            num6 = third_byte & 0x0F
            
            dtc_code = f"{prefix}{num1}{num2:X}{num3:X}{num4:X}"
            return dtc_code
            
        except Exception as e:
            logger.error(f"Error parsing DTC bytes: {e}")
            return None
    
    async def clear_dtc_information(self, ecu_id: int) -> bool:
        """
        Clear DTC information using UDS service 0x14
        """
        try:
            # Service 0x14 - Clear Diagnostic Information
            response = await self.send_uds_request(ecu_id, 0x14, b'\xFF\xFF\xFF')
            return response.success
            
        except Exception as e:
            logger.error(f"Error clearing DTC information from ECU {ecu_id:03X}: {e}")
            return False
    
    async def read_data_by_identifier(self, ecu_id: int, data_identifier: int) -> Optional[bytes]:
        """
        Read data by identifier using UDS service 0x22
        """
        try:
            # Service 0x22 - Read Data By Identifier
            did_bytes = data_identifier.to_bytes(2, 'big')
            response = await self.send_uds_request(ecu_id, 0x22, did_bytes)
            
            if response.success:
                return response.data[2:]  # Skip DID echo
            
        except Exception as e:
            logger.error(f"Error reading data identifier {data_identifier:04X} from ECU {ecu_id:03X}: {e}")
        
        return None
    
    async def write_data_by_identifier(self, ecu_id: int, data_identifier: int, data: bytes) -> bool:
        """
        Write data by identifier using UDS service 0x2E
        """
        try:
            # Service 0x2E - Write Data By Identifier
            did_bytes = data_identifier.to_bytes(2, 'big')
            request_data = did_bytes + data
            response = await self.send_uds_request(ecu_id, 0x2E, request_data)
            
            return response.success
            
        except Exception as e:
            logger.error(f"Error writing data identifier {data_identifier:04X} to ECU {ecu_id:03X}: {e}")
            return False
    
    async def start_diagnostic_session(self, ecu_id: int, session_type: int = 0x01) -> bool:
        """
        Start diagnostic session using UDS service 0x10
        """
        try:
            # Service 0x10 - Diagnostic Session Control
            response = await self.send_uds_request(ecu_id, 0x10, bytes([session_type]))
            return response.success
            
        except Exception as e:
            logger.error(f"Error starting diagnostic session on ECU {ecu_id:03X}: {e}")
            return False
    
    async def security_access(self, ecu_id: int, access_type: int = 0x01) -> Tuple[bool, Optional[bytes]]:
        """
        Perform security access using UDS service 0x27
        """
        try:
            # Service 0x27 - Security Access
            response = await self.send_uds_request(ecu_id, 0x27, bytes([access_type]))
            
            if response.success:
                if access_type % 2 == 1:  # Request seed
                    seed = response.data[1:] if len(response.data) > 1 else None
                    return True, seed
                else:  # Send key
                    return True, None
            
            return False, None
            
        except Exception as e:
            logger.error(f"Error in security access for ECU {ecu_id:03X}: {e}")
            return False, None
    
    async def scan_ecus(self) -> List[ECUInfo]:
        """
        Scan for available ECUs on the CAN bus
        """
        ecus = []
        
        # Common ECU IDs to scan
        ecu_ids = [
            (0x7E0, "Engine ECU"),
            (0x7E1, "Transmission ECU"),
            (0x7E2, "ABS ECU"),
            (0x7E3, "Airbag ECU"),
            (0x7E4, "Body Control Module"),
            (0x7E5, "Instrument Cluster"),
            (0x7E6, "Climate Control"),
            (0x7E7, "Gateway ECU")
        ]
        
        for ecu_id, name in ecu_ids:
            try:
                # Try to start diagnostic session
                if await self.start_diagnostic_session(ecu_id):
                    ecu_info = ECUInfo(ecu_id=ecu_id, name=name)
                    
                    # Try to read ECU information
                    try:
                        # Read software version (DID 0xF194)
                        sw_version = await self.read_data_by_identifier(ecu_id, 0xF194)
                        if sw_version:
                            ecu_info.software_version = sw_version.decode('ascii', errors='ignore')
                    except:
                        pass
                    
                    try:
                        # Read hardware version (DID 0xF193)
                        hw_version = await self.read_data_by_identifier(ecu_id, 0xF193)
                        if hw_version:
                            ecu_info.hardware_version = hw_version.decode('ascii', errors='ignore')
                    except:
                        pass
                    
                    ecus.append(ecu_info)
                    logger.info(f"Found ECU: {name} (ID: {ecu_id:03X})")
                    
            except Exception as e:
                logger.debug(f"ECU {ecu_id:03X} not responding: {e}")
        
        return ecus
