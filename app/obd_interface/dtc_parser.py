"""
DTC (Diagnostic Trouble Code) Parser
Handles interpretation and categorization of DTCs using SQLite database
"""
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..config import OBDConfig
from ..database.database import dtc_repository


logger = logging.getLogger(__name__)


@dataclass
class DTCInfo:
    """Detailed DTC information"""
    code: str
    description: str
    category: str
    severity: str
    system: str
    possible_causes: List[str]
    repair_hints: List[str]
    related_pids: List[str]
    freeze_frame_required: bool = False
    brand_specific_info: Dict[str, Any] = None


class DTCParser:
    """
    DTC Parser for interpreting diagnostic trouble codes using SQLite database
    """

    def __init__(self):
        self.dtc_repository = dtc_repository
        # Cache for frequently accessed DTCs
        self._dtc_cache: Dict[str, DTCInfo] = {}

    def get_dtc_from_database(self, dtc_code: str) -> Optional[DTCInfo]:
        """
        Get DTC information from SQLite database
        """
        # Check cache first
        if dtc_code in self._dtc_cache:
            return self._dtc_cache[dtc_code]

        try:
            dtc_record = self.dtc_repository.get_dtc_by_code(dtc_code)
            if dtc_record:
                dtc_info = DTCInfo(
                    code=dtc_record.code,
                    description=dtc_record.description,
                    category=dtc_record.category,
                    severity=dtc_record.severity,
                    system=dtc_record.system,
                    possible_causes=dtc_record.possible_causes or [],
                    repair_hints=dtc_record.repair_hints or [],
                    related_pids=dtc_record.related_pids or [],
                    freeze_frame_required=dtc_record.freeze_frame_required,
                    brand_specific_info=dtc_record.brand_specific_info or {}
                )

                # Cache the result
                self._dtc_cache[dtc_code] = dtc_info
                return dtc_info

        except Exception as e:
            logger.error(f"Error retrieving DTC {dtc_code} from database: {e}")

        return None

    def search_dtcs(self, search_term: str, limit: int = 20) -> List[DTCInfo]:
        """
        Search DTCs in database
        """
        try:
            dtc_records = self.dtc_repository.search_dtcs(
                search_term=search_term,
                limit=limit
            )

            dtc_infos = []
            for record in dtc_records:
                dtc_info = DTCInfo(
                    code=record.code,
                    description=record.description,
                    category=record.category,
                    severity=record.severity,
                    system=record.system,
                    possible_causes=record.possible_causes or [],
                    repair_hints=record.repair_hints or [],
                    related_pids=record.related_pids or [],
                    freeze_frame_required=record.freeze_frame_required,
                    brand_specific_info=record.brand_specific_info or {}
                )
                dtc_infos.append(dtc_info)

            return dtc_infos

        except Exception as e:
            logger.error(f"Error searching DTCs: {e}")
            return []
            "P0171": {
                "description": "System Too Lean (Bank 1)",
                "category": "Powertrain",
                "severity": "Medium",
                "system": "Fuel and Air Metering",
                "possible_causes": [
                    "Vacuum leak",
                    "Faulty fuel pump",
                    "Clogged fuel filter",
                    "Faulty oxygen sensor",
                    "Faulty MAF sensor"
                ],
                "repair_hints": [
                    "Check for vacuum leaks",
                    "Test fuel pressure",
                    "Replace fuel filter",
                    "Test oxygen sensors",
                    "Clean MAF sensor"
                ],
                "related_pids": ["0x06", "0x07", "0x10", "0x14"]
            },
            "P0300": {
                "description": "Random/Multiple Cylinder Misfire Detected",
                "category": "Powertrain",
                "severity": "High",
                "system": "Ignition System",
                "possible_causes": [
                    "Faulty spark plugs",
                    "Faulty ignition coils",
                    "Fuel delivery issues",
                    "Compression problems",
                    "Vacuum leaks"
                ],
                "repair_hints": [
                    "Replace spark plugs",
                    "Test ignition coils",
                    "Check fuel pressure",
                    "Perform compression test",
                    "Check for vacuum leaks"
                ],
                "related_pids": ["0x0C", "0x04"]
            },
            "P0420": {
                "description": "Catalyst System Efficiency Below Threshold (Bank 1)",
                "category": "Powertrain",
                "severity": "Medium",
                "system": "Emission Control",
                "possible_causes": [
                    "Faulty catalytic converter",
                    "Faulty oxygen sensors",
                    "Engine misfire",
                    "Fuel system issues"
                ],
                "repair_hints": [
                    "Replace catalytic converter",
                    "Test oxygen sensors",
                    "Fix any misfires",
                    "Check fuel system"
                ],
                "related_pids": ["0x14", "0x15"]
            }
        }
    
    def parse_dtc(self, dtc_code: str) -> DTCInfo:
        """
        Parse a single DTC code and return detailed information
        """
        # Normalize DTC code
        dtc_code = dtc_code.upper().strip()

        # Try to get from database first
        dtc_info = self.get_dtc_from_database(dtc_code)
        if dtc_info:
            return dtc_info

        # If not found in database, create basic info from code structure
        category = self._get_dtc_category(dtc_code)
        system = self._get_dtc_system(dtc_code)

        # Return basic info for unknown DTCs
        return DTCInfo(
            code=dtc_code,
            description=f"Unknown DTC: {dtc_code}",
            category=category,
            severity="unknown",
            system=system,
            possible_causes=["Unknown - requires further diagnosis"],
            repair_hints=["Consult service manual or professional technician"],
            related_pids=[],
            freeze_frame_required=False,
            brand_specific_info={}
        )
    
    def _get_dtc_category(self, dtc_code: str) -> str:
        """
        Determine DTC category from the first character
        """
        if len(dtc_code) > 0:
            prefix = dtc_code[0].upper()
            return OBDConfig.DTC_PREFIXES.get(prefix, "Unknown")
        return "Unknown"
    
    def _get_dtc_system(self, dtc_code: str) -> str:
        """
        Determine system from DTC code structure
        """
        if len(dtc_code) >= 4:
            try:
                # Extract system code (second and third characters)
                system_code = dtc_code[1:3]
                
                # Powertrain system mapping
                if dtc_code.startswith('P'):
                    powertrain_systems = {
                        '00': 'Fuel and Air Metering',
                        '01': 'Fuel and Air Metering',
                        '02': 'Fuel Injector Circuit',
                        '03': 'Ignition System',
                        '04': 'Auxiliary Emission Control',
                        '05': 'Vehicle Speed Control',
                        '06': 'Computer and Auxiliary Outputs',
                        '07': 'Transmission',
                        '08': 'Transmission',
                        '09': 'SAE Reserved',
                        '10': 'SAE Reserved'
                    }
                    return powertrain_systems.get(system_code, "Powertrain - Other")
                
                # Body system mapping
                elif dtc_code.startswith('B'):
                    return "Body System"
                
                # Chassis system mapping
                elif dtc_code.startswith('C'):
                    return "Chassis System"
                
                # Network system mapping
                elif dtc_code.startswith('U'):
                    return "Network/Communication"
                    
            except:
                pass
        
        return "Unknown System"
    
    def parse_multiple_dtcs(self, dtc_codes: List[str]) -> List[DTCInfo]:
        """
        Parse multiple DTC codes
        """
        return [self.parse_dtc(code) for code in dtc_codes]
    
    def categorize_dtcs(self, dtc_infos: List[DTCInfo]) -> Dict[str, List[DTCInfo]]:
        """
        Categorize DTCs by severity and system
        """
        categorized = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": [],
            "info": [],
            "unknown": []
        }
        
        for dtc_info in dtc_infos:
            severity = dtc_info.severity.lower()
            if severity in categorized:
                categorized[severity].append(dtc_info)
            else:
                categorized["unknown"].append(dtc_info)
        
        return categorized
    
    def get_priority_dtcs(self, dtc_infos: List[DTCInfo]) -> List[DTCInfo]:
        """
        Get DTCs sorted by priority (severity)
        """
        severity_order = {
            "critical": 0,
            "high": 1,
            "medium": 2,
            "low": 3,
            "info": 4,
            "unknown": 5
        }
        
        return sorted(
            dtc_infos,
            key=lambda x: severity_order.get(x.severity.lower(), 5)
        )
    
    def get_related_dtcs(self, dtc_code: str) -> List[str]:
        """
        Get DTCs that are commonly related to the given DTC
        """
        related = []
        
        # Check for common patterns
        if dtc_code.startswith('P030'):  # Misfire codes
            related.extend(['P0300', 'P0301', 'P0302', 'P0303', 'P0304'])
        elif dtc_code.startswith('P017'):  # Lean codes
            related.extend(['P0171', 'P0174'])
        elif dtc_code.startswith('P042'):  # Catalyst codes
            related.extend(['P0420', 'P0430'])
        
        # Remove the original code from related list
        if dtc_code in related:
            related.remove(dtc_code)
        
        return related
    
    def generate_diagnostic_summary(self, dtc_infos: List[DTCInfo]) -> Dict[str, Any]:
        """
        Generate a comprehensive diagnostic summary
        """
        if not dtc_infos:
            return {
                "total_dtcs": 0,
                "status": "No DTCs found",
                "severity_breakdown": {},
                "system_breakdown": {},
                "recommendations": ["Vehicle appears to be operating normally"]
            }
        
        # Count by severity
        severity_counts = {}
        system_counts = {}
        all_causes = []
        all_hints = []
        
        for dtc in dtc_infos:
            # Count severities
            severity = dtc.severity.lower()
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Count systems
            system = dtc.system
            system_counts[system] = system_counts.get(system, 0) + 1
            
            # Collect causes and hints
            all_causes.extend(dtc.possible_causes)
            all_hints.extend(dtc.repair_hints)
        
        # Determine overall status
        if "critical" in severity_counts or "high" in severity_counts:
            status = "Immediate attention required"
        elif "medium" in severity_counts:
            status = "Service recommended"
        else:
            status = "Minor issues detected"
        
        # Generate recommendations
        recommendations = []
        if "critical" in severity_counts or "high" in severity_counts:
            recommendations.append("Stop driving and seek immediate professional service")
        elif "medium" in severity_counts:
            recommendations.append("Schedule service appointment soon")
        
        # Add most common repair hints
        unique_hints = list(set(all_hints))[:5]  # Top 5 unique hints
        recommendations.extend(unique_hints)
        
        return {
            "total_dtcs": len(dtc_infos),
            "status": status,
            "severity_breakdown": severity_counts,
            "system_breakdown": system_counts,
            "recommendations": recommendations,
            "most_common_causes": list(set(all_causes))[:10]  # Top 10 unique causes
        }
