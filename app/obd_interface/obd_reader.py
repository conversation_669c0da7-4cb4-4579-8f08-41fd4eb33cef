"""
OBD2 Reader implementation using python-OBD library
Supports ELM327 adapters via Bluetooth and USB
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime

import obd
from obd import OBDStatus
import serial
import serial.tools.list_ports

from ..config import settings, OBDConfig


logger = logging.getLogger(__name__)


@dataclass
class DTCResult:
    """Data class for DTC (Diagnostic Trouble Code) results"""
    code: str
    description: str
    status: str
    timestamp: datetime
    freeze_frame_data: Optional[Dict[str, Any]] = None


@dataclass
class OBDParameter:
    """Data class for OBD parameter readings"""
    pid: str
    name: str
    value: Any
    unit: str
    timestamp: datetime


class OBDReader:
    """
    OBD2 Reader class for communicating with ELM327 adapters
    """
    
    def __init__(self, port: Optional[str] = None, baudrate: int = 38400):
        self.port = port or settings.obd_port
        self.baudrate = baudrate
        self.connection: Optional[obd.OBD] = None
        self.is_connected = False
        
    async def connect(self) -> bool:
        """
        Establish connection to OBD2 adapter
        """
        try:
            if self.port:
                # Connect to specific port
                self.connection = obd.OBD(self.port, baudrate=self.baudrate)
            else:
                # Auto-detect port
                self.connection = obd.OBD()
            
            self.is_connected = self.connection.status() == OBDStatus.CAR_CONNECTED
            
            if self.is_connected:
                logger.info(f"Successfully connected to OBD2 adapter on {self.connection.port_name()}")
                return True
            else:
                logger.error(f"Failed to connect to vehicle. Status: {self.connection.status()}")
                return False
                
        except Exception as e:
            logger.error(f"Error connecting to OBD2 adapter: {e}")
            return False
    
    async def disconnect(self):
        """
        Close OBD2 connection
        """
        if self.connection:
            self.connection.close()
            self.is_connected = False
            logger.info("OBD2 connection closed")
    
    @staticmethod
    def list_available_ports() -> List[str]:
        """
        List all available serial ports
        """
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    async def get_supported_pids(self) -> List[str]:
        """
        Get list of supported PIDs from the vehicle
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        supported_pids = []
        try:
            # Query supported PIDs
            cmd = obd.commands.GET_SUPPORTED_PIDS
            response = self.connection.query(cmd)
            
            if response.value:
                supported_pids = [str(pid) for pid in response.value]
                
        except Exception as e:
            logger.error(f"Error getting supported PIDs: {e}")
        
        return supported_pids
    
    async def read_dtcs(self) -> List[DTCResult]:
        """
        Read Diagnostic Trouble Codes from the vehicle
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        dtcs = []
        try:
            # Read stored DTCs
            cmd = obd.commands.GET_DTC
            response = self.connection.query(cmd)
            
            if response.value:
                for dtc_tuple in response.value:
                    code, description = dtc_tuple
                    dtc = DTCResult(
                        code=code,
                        description=description,
                        status="stored",
                        timestamp=datetime.now()
                    )
                    dtcs.append(dtc)
            
            # Read pending DTCs
            try:
                pending_cmd = obd.commands.GET_PENDING_DTC
                pending_response = self.connection.query(pending_cmd)
                
                if pending_response.value:
                    for dtc_tuple in pending_response.value:
                        code, description = dtc_tuple
                        dtc = DTCResult(
                            code=code,
                            description=description,
                            status="pending",
                            timestamp=datetime.now()
                        )
                        dtcs.append(dtc)
            except:
                pass  # Some vehicles don't support pending DTCs
                
        except Exception as e:
            logger.error(f"Error reading DTCs: {e}")
        
        return dtcs
    
    async def clear_dtcs(self) -> bool:
        """
        Clear all stored DTCs
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            cmd = obd.commands.CLEAR_DTC
            response = self.connection.query(cmd)
            
            if response.is_null():
                logger.error("Failed to clear DTCs")
                return False
            
            logger.info("DTCs cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing DTCs: {e}")
            return False
    
    async def read_parameter(self, pid: str) -> Optional[OBDParameter]:
        """
        Read a specific OBD parameter by PID
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            # Get command by PID
            cmd = obd.commands.has_pid(pid)
            if not cmd:
                logger.warning(f"PID {pid} not supported")
                return None
            
            response = self.connection.query(cmd)
            
            if response.value is not None:
                return OBDParameter(
                    pid=pid,
                    name=cmd.desc,
                    value=response.value.magnitude if hasattr(response.value, 'magnitude') else response.value,
                    unit=str(response.value.units) if hasattr(response.value, 'units') else "",
                    timestamp=datetime.now()
                )
            
        except Exception as e:
            logger.error(f"Error reading parameter {pid}: {e}")
        
        return None
    
    async def read_multiple_parameters(self, pids: List[str]) -> List[OBDParameter]:
        """
        Read multiple OBD parameters
        """
        parameters = []
        
        for pid in pids:
            param = await self.read_parameter(pid)
            if param:
                parameters.append(param)
        
        return parameters
    
    async def get_vehicle_info(self) -> Dict[str, Any]:
        """
        Get basic vehicle information
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        info = {}
        
        try:
            # VIN
            try:
                vin_cmd = obd.commands.VIN
                vin_response = self.connection.query(vin_cmd)
                if vin_response.value:
                    info['vin'] = vin_response.value
            except:
                pass
            
            # ECU Name
            try:
                ecu_cmd = obd.commands.ELM_ECU_NAME
                ecu_response = self.connection.query(ecu_cmd)
                if ecu_response.value:
                    info['ecu_name'] = ecu_response.value
            except:
                pass
            
            # Protocol
            info['protocol'] = str(self.connection.protocol_name())
            
            # Supported commands
            info['supported_commands'] = len(self.connection.supported_commands)
            
        except Exception as e:
            logger.error(f"Error getting vehicle info: {e}")
        
        return info
    
    async def get_freeze_frame_data(self, dtc_code: str) -> Optional[Dict[str, Any]]:
        """
        Get freeze frame data for a specific DTC
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            # Read freeze frame data
            cmd = obd.commands.GET_FREEZE_DTC
            response = self.connection.query(cmd)
            
            if response.value:
                # Parse freeze frame data
                freeze_data = {}
                # Implementation depends on the specific format returned
                # This is a simplified version
                freeze_data['dtc'] = dtc_code
                freeze_data['data'] = str(response.value)
                freeze_data['timestamp'] = datetime.now()
                
                return freeze_data
                
        except Exception as e:
            logger.error(f"Error getting freeze frame data for {dtc_code}: {e}")
        
        return None
    
    async def monitor_real_time(self, pids: List[str], duration: int = 60) -> List[OBDParameter]:
        """
        Monitor real-time data for specified duration
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        readings = []
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < duration:
            for pid in pids:
                param = await self.read_parameter(pid)
                if param:
                    readings.append(param)
            
            await asyncio.sleep(1)  # 1 second interval
        
        return readings
