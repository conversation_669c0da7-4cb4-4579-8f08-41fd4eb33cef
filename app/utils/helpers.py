"""
Utility functions and helpers for the OBD2 diagnostic system
"""
import re
import json
import hashlib
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path


def validate_vin(vin: str) -> bool:
    """
    Validate Vehicle Identification Number (VIN)
    """
    if not vin or len(vin) != 17:
        return False
    
    # VIN should not contain I, O, Q
    if any(char in vin.upper() for char in ['I', 'O', 'Q']):
        return False
    
    # Basic pattern check (alphanumeric)
    if not re.match(r'^[A-HJ-NPR-Z0-9]{17}$', vin.upper()):
        return False
    
    return True


def parse_vin(vin: str) -> Dict[str, str]:
    """
    Parse VIN to extract vehicle information
    """
    if not validate_vin(vin):
        return {}
    
    vin = vin.upper()
    
    # Basic VIN parsing (simplified)
    return {
        "wmi": vin[:3],  # World Manufacturer Identifier
        "vds": vin[3:9],  # Vehicle Descriptor Section
        "vis": vin[9:],   # Vehicle Identifier Section
        "year_code": vin[9],
        "plant_code": vin[10],
        "serial": vin[11:]
    }


def get_model_year_from_vin(vin: str) -> Optional[int]:
    """
    Extract model year from VIN
    """
    if not validate_vin(vin):
        return None
    
    year_codes = {
        'A': 1980, 'B': 1981, 'C': 1982, 'D': 1983, 'E': 1984,
        'F': 1985, 'G': 1986, 'H': 1987, 'J': 1988, 'K': 1989,
        'L': 1990, 'M': 1991, 'N': 1992, 'P': 1993, 'R': 1994,
        'S': 1995, 'T': 1996, 'V': 1997, 'W': 1998, 'X': 1999,
        'Y': 2000, '1': 2001, '2': 2002, '3': 2003, '4': 2004,
        '5': 2005, '6': 2006, '7': 2007, '8': 2008, '9': 2009,
        'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014,
        'F': 2015, 'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019,
        'L': 2020, 'M': 2021, 'N': 2022, 'P': 2023, 'R': 2024
    }
    
    year_code = vin[9].upper()
    base_year = year_codes.get(year_code)
    
    if base_year:
        # Handle 30-year cycle
        current_year = datetime.now().year
        if base_year < 2000 and current_year >= 2010:
            base_year += 30
        
        return base_year
    
    return None


def format_dtc_code(code: str) -> str:
    """
    Format DTC code to standard format (e.g., P0171)
    """
    if not code:
        return ""
    
    code = code.upper().strip()
    
    # Remove any spaces or dashes
    code = re.sub(r'[\s\-]', '', code)
    
    # Ensure it starts with a letter
    if not re.match(r'^[PBCU]', code):
        return code
    
    # Ensure proper format
    if re.match(r'^[PBCU][0-9A-F]{4}$', code):
        return code
    
    return code


def calculate_mileage_interval(current_mileage: int, interval: int) -> Dict[str, int]:
    """
    Calculate next service intervals based on current mileage
    """
    next_service = ((current_mileage // interval) + 1) * interval
    miles_to_service = next_service - current_mileage
    last_service = (current_mileage // interval) * interval
    miles_since_service = current_mileage - last_service
    
    return {
        "next_service_mileage": next_service,
        "miles_to_next_service": miles_to_service,
        "last_service_mileage": last_service,
        "miles_since_last_service": miles_since_service
    }


def estimate_repair_time(complexity: str, parts_count: int = 1) -> str:
    """
    Estimate repair time based on complexity
    """
    base_times = {
        "simple": 0.5,    # 30 minutes
        "basic": 1.0,     # 1 hour
        "moderate": 2.0,  # 2 hours
        "complex": 4.0,   # 4 hours
        "major": 8.0      # 8 hours
    }
    
    base_time = base_times.get(complexity.lower(), 2.0)
    total_time = base_time * parts_count
    
    if total_time < 1:
        return f"{int(total_time * 60)} minutes"
    elif total_time < 8:
        return f"{total_time:.1f} hours"
    else:
        days = total_time / 8
        return f"{days:.1f} days"


def generate_session_id() -> str:
    """
    Generate unique session ID
    """
    timestamp = datetime.now().isoformat()
    hash_input = f"{timestamp}_{id(object())}"
    return hashlib.md5(hash_input.encode()).hexdigest()[:12]


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file operations
    """
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename


def parse_obd_response(response_bytes: bytes) -> Dict[str, Any]:
    """
    Parse raw OBD response bytes
    """
    if not response_bytes:
        return {"error": "Empty response"}
    
    try:
        # Convert bytes to hex string
        hex_string = response_bytes.hex().upper()
        
        # Parse based on first byte (mode)
        mode = int(hex_string[:2], 16)
        
        result = {
            "mode": mode,
            "raw_data": hex_string,
            "length": len(response_bytes)
        }
        
        if mode == 0x41:  # Mode 01 response
            if len(hex_string) >= 4:
                pid = int(hex_string[2:4], 16)
                data = hex_string[4:]
                result.update({
                    "pid": f"0x{pid:02X}",
                    "data": data
                })
        
        elif mode == 0x43:  # Mode 03 response (DTCs)
            dtc_count = int(hex_string[2:4], 16)
            dtcs = []
            
            for i in range(dtc_count):
                start_idx = 4 + (i * 4)
                if start_idx + 4 <= len(hex_string):
                    dtc_bytes = hex_string[start_idx:start_idx + 4]
                    dtc_code = parse_dtc_bytes(dtc_bytes)
                    if dtc_code:
                        dtcs.append(dtc_code)
            
            result.update({
                "dtc_count": dtc_count,
                "dtcs": dtcs
            })
        
        return result
        
    except Exception as e:
        return {"error": f"Parse error: {str(e)}"}


def parse_dtc_bytes(dtc_hex: str) -> Optional[str]:
    """
    Parse DTC from hex bytes
    """
    if len(dtc_hex) != 4:
        return None
    
    try:
        first_byte = int(dtc_hex[:2], 16)
        second_byte = int(dtc_hex[2:4], 16)
        
        # Determine prefix
        prefix_map = {
            0x0: 'P0', 0x1: 'P1', 0x2: 'P2', 0x3: 'P3',
            0x4: 'B0', 0x5: 'B1', 0x6: 'B2', 0x7: 'B3',
            0x8: 'C0', 0x9: 'C1', 0xA: 'C2', 0xB: 'C3',
            0xC: 'U0', 0xD: 'U1', 0xE: 'U2', 0xF: 'U3'
        }
        
        prefix = prefix_map.get(first_byte >> 6, 'P0')
        
        # Extract numeric part
        num_part = ((first_byte & 0x3F) << 8) | second_byte
        
        return f"{prefix}{num_part:03X}"
        
    except:
        return None


def load_json_data(file_path: str) -> Dict[str, Any]:
    """
    Load JSON data from file with error handling
    """
    try:
        path = Path(file_path)
        if not path.exists():
            return {}
        
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON from {file_path}: {e}")
        return {}


def save_json_data(data: Dict[str, Any], file_path: str) -> bool:
    """
    Save data to JSON file with error handling
    """
    try:
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"Error saving JSON to {file_path}: {e}")
        return False


def format_currency(amount: float, currency: str = "USD") -> str:
    """
    Format currency amount
    """
    if currency.upper() == "USD":
        return f"${amount:,.2f}"
    elif currency.upper() == "EUR":
        return f"€{amount:,.2f}"
    elif currency.upper() == "GBP":
        return f"£{amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"


def calculate_confidence_score(factors: Dict[str, float]) -> float:
    """
    Calculate confidence score based on multiple factors
    """
    if not factors:
        return 0.0
    
    # Weighted average of factors
    weights = {
        "data_quality": 0.3,
        "dtc_count": 0.2,
        "parameter_availability": 0.2,
        "vehicle_info_completeness": 0.15,
        "ai_model_confidence": 0.15
    }
    
    total_score = 0.0
    total_weight = 0.0
    
    for factor, score in factors.items():
        weight = weights.get(factor, 0.1)
        total_score += score * weight
        total_weight += weight
    
    if total_weight == 0:
        return 0.0
    
    return min(total_score / total_weight, 1.0)


def get_severity_color(severity: str) -> str:
    """
    Get color code for severity level
    """
    colors = {
        "critical": "#FF0000",  # Red
        "high": "#FF6600",      # Orange
        "medium": "#FFCC00",    # Yellow
        "low": "#00CC00",       # Green
        "info": "#0066CC",      # Blue
        "unknown": "#666666"    # Gray
    }
    
    return colors.get(severity.lower(), "#666666")


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format
    """
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"
