# Open Source References

<PERSON><PERSON> klas<PERSON>r, OBD2 ve araç teşhis sistemleri için açık kaynak referans kodlarını içerir. Ana proje bu kodlara doğrudan bağıml<PERSON> değil<PERSON>, ancak geliştirme ve test amaçlı kullanılabilir.

## 📁 Klasör Yapısı

### `pyren/` - Renault Diagnostics
- **Kaynak**: Renault araçları için Python tabanlı teşhis araçları
- **Protokoller**: KWP2000, UDS
- **Özellikler**: ECU okuma/yazma, DTC analizi, parametre ayarlama

### `ddt4all/` - Renault DDT4ALL
- **Kaynak**: Renault/Dacia araçları için kapsamlı teşhis aracı
- **Protokoller**: KWP2000, UDS, CAN
- **Özellikler**: ECU programlama, adaptasyon, teşhis

### `obdlib/` - Generic OBD Library
- **Kaynak**: Genel amaçlı OBD2 kütüphanesi
- **Protokoller**: OBD2, ISO 14230, ISO 15765
- **Özellikler**: Multi-protokol desteği, DTC okuma

### `vag-uds/` - VAG UDS Implementation
- **Kaynak**: Volkswagen Grubu UDS implementasyonu
- **Protokoller**: UDS, KWP2000
- **Özellikler**: VAG özel komutları, adaptasyon prosedürleri

### `bmw-ediabas/` - BMW EDIABAS
- **Kaynak**: BMW teşhis protokolü implementasyonu
- **Protokoller**: EDIABAS, UDS
- **Özellikler**: BMW özel komutları, kodlama

### `toyota-techstream/` - Toyota Techstream
- **Kaynak**: Toyota teşhis protokolü referansları
- **Protokoller**: ISO 14230, proprietary
- **Özellikler**: Toyota özel PIDs, hibrit sistem teşhisi

## 🔧 Kullanım

### Ana Projede Referans Kullanımı

```python
# Ana projede referans kodları import etme (opsiyonel)
try:
    from references.pyren import renault_dtc_parser
    RENAULT_SUPPORT = True
except ImportError:
    RENAULT_SUPPORT = False

# Referans kodlarından öğrenilen yapıları ana projeye entegre etme
class EnhancedDTCParser:
    def __init__(self):
        # Referans kodlarından öğrenilen best practices
        self.brand_parsers = {}
        if RENAULT_SUPPORT:
            self.brand_parsers['renault'] = renault_dtc_parser
```

### Test ve Geliştirme

```python
# Test amaçlı referans kodları kullanma
import sys
sys.path.append('references')

from pyren.dtc_decoder import decode_renault_dtc
from vag_uds.adaptation import perform_vag_adaptation
```

## 📋 Entegrasyon Stratejisi

1. **Kod İnceleme**: Referans kodları inceleyerek best practices öğrenme
2. **Protokol Analizi**: Marka özel protokolleri anlama
3. **Test Verileri**: Referans test verilerini kullanma
4. **Algoritma Adaptasyonu**: Başarılı algoritmaları ana projeye uyarlama

## ⚠️ Önemli Notlar

- Ana proje bu klasöre doğrudan bağımlı değildir
- Referans kodları sadece öğrenme ve test amaçlıdır
- Lisans uyumluluğuna dikkat edilmelidir
- Üretim ortamında kullanım önerilmez

## 🔗 Kaynak Linkler

- [python-OBD](https://github.com/brendan-w/python-OBD)
- [VAG-UDS](https://github.com/juergenwest/VAG-UDS)
- [BMW Ediabas](https://github.com/uholeschak/ediabaslib)
- [Toyota PIDs](https://github.com/iwanders/OBD-PIDs-for-Hybrid-Cars)
- [DDT4ALL](https://github.com/cedricp/ddt4all)

## 📝 Lisanslar

Her alt klasör kendi lisans dosyasını içerir. Ana proje MIT lisansı altındadır.
