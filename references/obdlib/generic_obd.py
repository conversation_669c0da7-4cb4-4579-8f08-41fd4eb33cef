"""
Generic OBD Library Reference Implementation
Based on various open-source OBD libraries

This provides reference patterns for multi-protocol OBD communication
that can be learned and adapted for the main project.
"""
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass
from enum import Enum
import time


class OBDProtocol(Enum):
    """OBD communication protocols"""
    AUTO = "auto"
    ISO_9141_2 = "iso_9141_2"
    ISO_14230_4_KWP = "iso_14230_4_kwp"
    ISO_15765_4_CAN = "iso_15765_4_can"
    SAE_J1850_PWM = "sae_j1850_pwm"
    SAE_J1850_VPW = "sae_j1850_vpw"


@dataclass
class OBDCommand:
    """OBD command structure"""
    name: str
    description: str
    mode: int
    pid: int
    response_length: int
    decoder: Callable[[bytes], Any]
    supported_protocols: List[OBDProtocol]


@dataclass
class OBDResponse:
    """OBD response structure"""
    command: OBDCommand
    raw_data: bytes
    value: Any
    unit: str
    timestamp: float
    protocol_used: OBDProtocol


class GenericOBDDecoder:
    """
    Reference implementation of OBD response decoders
    Shows common patterns for decoding OBD responses
    """
    
    @staticmethod
    def decode_engine_rpm(data: bytes) -> float:
        """Decode engine RPM (PID 0x0C)"""
        if len(data) >= 2:
            return ((data[0] * 256) + data[1]) / 4.0
        return 0.0
    
    @staticmethod
    def decode_vehicle_speed(data: bytes) -> float:
        """Decode vehicle speed (PID 0x0D)"""
        if len(data) >= 1:
            return float(data[0])
        return 0.0
    
    @staticmethod
    def decode_coolant_temp(data: bytes) -> float:
        """Decode coolant temperature (PID 0x05)"""
        if len(data) >= 1:
            return float(data[0]) - 40.0
        return -40.0
    
    @staticmethod
    def decode_throttle_position(data: bytes) -> float:
        """Decode throttle position (PID 0x11)"""
        if len(data) >= 1:
            return (data[0] * 100.0) / 255.0
        return 0.0
    
    @staticmethod
    def decode_maf_flow(data: bytes) -> float:
        """Decode MAF air flow rate (PID 0x10)"""
        if len(data) >= 2:
            return ((data[0] * 256) + data[1]) / 100.0
        return 0.0
    
    @staticmethod
    def decode_fuel_trim(data: bytes) -> float:
        """Decode fuel trim (PIDs 0x06, 0x07, 0x08, 0x09)"""
        if len(data) >= 1:
            return ((data[0] - 128) * 100.0) / 128.0
        return 0.0
    
    @staticmethod
    def decode_o2_sensor(data: bytes) -> tuple:
        """Decode O2 sensor voltage and fuel trim"""
        if len(data) >= 2:
            voltage = data[0] / 200.0
            fuel_trim = ((data[1] - 128) * 100.0) / 128.0
            return (voltage, fuel_trim)
        return (0.0, 0.0)
    
    @staticmethod
    def decode_dtc_count(data: bytes) -> int:
        """Decode DTC count from Mode 01 PID 01"""
        if len(data) >= 1:
            return data[0] & 0x7F  # Lower 7 bits
        return 0
    
    @staticmethod
    def decode_supported_pids(data: bytes) -> List[int]:
        """Decode supported PIDs bitmap"""
        supported = []
        if len(data) >= 4:
            for byte_idx, byte_val in enumerate(data):
                for bit_idx in range(8):
                    if byte_val & (1 << (7 - bit_idx)):
                        pid = (byte_idx * 8) + bit_idx + 1
                        supported.append(pid)
        return supported


class OBDCommandDatabase:
    """
    Reference implementation of OBD command database
    Shows how to organize and manage OBD commands
    """
    
    def __init__(self):
        self.commands = self._build_command_database()
    
    def _build_command_database(self) -> Dict[str, OBDCommand]:
        """Build comprehensive OBD command database"""
        decoder = GenericOBDDecoder()
        
        return {
            # Mode 01 - Show current data
            "PIDS_A": OBDCommand(
                name="Supported PIDs [01-20]",
                description="PIDs supported in range 01-20",
                mode=0x01, pid=0x00, response_length=4,
                decoder=decoder.decode_supported_pids,
                supported_protocols=list(OBDProtocol)
            ),
            
            "ENGINE_RPM": OBDCommand(
                name="Engine RPM",
                description="Engine revolutions per minute",
                mode=0x01, pid=0x0C, response_length=2,
                decoder=decoder.decode_engine_rpm,
                supported_protocols=list(OBDProtocol)
            ),
            
            "VEHICLE_SPEED": OBDCommand(
                name="Vehicle Speed",
                description="Vehicle speed",
                mode=0x01, pid=0x0D, response_length=1,
                decoder=decoder.decode_vehicle_speed,
                supported_protocols=list(OBDProtocol)
            ),
            
            "COOLANT_TEMP": OBDCommand(
                name="Coolant Temperature",
                description="Engine coolant temperature",
                mode=0x01, pid=0x05, response_length=1,
                decoder=decoder.decode_coolant_temp,
                supported_protocols=list(OBDProtocol)
            ),
            
            "THROTTLE_POS": OBDCommand(
                name="Throttle Position",
                description="Throttle position",
                mode=0x01, pid=0x11, response_length=1,
                decoder=decoder.decode_throttle_position,
                supported_protocols=list(OBDProtocol)
            ),
            
            "MAF_FLOW": OBDCommand(
                name="MAF Flow Rate",
                description="Mass air flow rate",
                mode=0x01, pid=0x10, response_length=2,
                decoder=decoder.decode_maf_flow,
                supported_protocols=list(OBDProtocol)
            ),
            
            "SHORT_FUEL_TRIM_1": OBDCommand(
                name="Short Term Fuel Trim Bank 1",
                description="Short term fuel trim - Bank 1",
                mode=0x01, pid=0x06, response_length=1,
                decoder=decoder.decode_fuel_trim,
                supported_protocols=list(OBDProtocol)
            ),
            
            "LONG_FUEL_TRIM_1": OBDCommand(
                name="Long Term Fuel Trim Bank 1",
                description="Long term fuel trim - Bank 1",
                mode=0x01, pid=0x07, response_length=1,
                decoder=decoder.decode_fuel_trim,
                supported_protocols=list(OBDProtocol)
            ),
            
            "O2_B1S1": OBDCommand(
                name="O2 Sensor Bank 1 Sensor 1",
                description="Oxygen sensor Bank 1, Sensor 1",
                mode=0x01, pid=0x14, response_length=2,
                decoder=decoder.decode_o2_sensor,
                supported_protocols=list(OBDProtocol)
            )
        }
    
    def get_command(self, name: str) -> Optional[OBDCommand]:
        """Get command by name"""
        return self.commands.get(name)
    
    def get_commands_by_mode(self, mode: int) -> List[OBDCommand]:
        """Get all commands for a specific mode"""
        return [cmd for cmd in self.commands.values() if cmd.mode == mode]
    
    def get_supported_commands(self, protocol: OBDProtocol) -> List[OBDCommand]:
        """Get commands supported by a specific protocol"""
        return [cmd for cmd in self.commands.values() 
                if protocol in cmd.supported_protocols]


class OBDProtocolHandler:
    """
    Reference implementation of protocol-specific handling
    Shows patterns for managing different OBD protocols
    """
    
    def __init__(self):
        self.protocol_configs = {
            OBDProtocol.ISO_9141_2: {
                "baud_rate": 10400,
                "init_sequence": self._iso9141_init,
                "frame_format": self._iso9141_frame,
                "checksum": self._iso9141_checksum
            },
            
            OBDProtocol.ISO_14230_4_KWP: {
                "baud_rate": 10400,
                "init_sequence": self._kwp2000_init,
                "frame_format": self._kwp2000_frame,
                "checksum": self._kwp2000_checksum
            },
            
            OBDProtocol.ISO_15765_4_CAN: {
                "baud_rate": 500000,
                "init_sequence": self._can_init,
                "frame_format": self._can_frame,
                "checksum": None  # CAN has built-in error detection
            },
            
            OBDProtocol.SAE_J1850_PWM: {
                "baud_rate": 41600,
                "init_sequence": self._j1850_init,
                "frame_format": self._j1850_frame,
                "checksum": self._j1850_checksum
            }
        }
    
    def _iso9141_init(self) -> bool:
        """ISO 9141-2 initialization sequence"""
        print("Performing ISO 9141-2 initialization")
        # 5-baud init sequence
        return True
    
    def _iso9141_frame(self, data: bytes) -> bytes:
        """Format data for ISO 9141-2 transmission"""
        # Header + Length + Data + Checksum
        header = b'\x68\x6A\xF1'
        length = len(data).to_bytes(1, 'big')
        checksum = self._iso9141_checksum(header + length + data)
        return header + length + data + checksum
    
    def _iso9141_checksum(self, data: bytes) -> bytes:
        """Calculate ISO 9141-2 checksum"""
        return (sum(data) & 0xFF).to_bytes(1, 'big')
    
    def _kwp2000_init(self) -> bool:
        """KWP2000 initialization sequence"""
        print("Performing KWP2000 initialization")
        # Fast or slow init
        return True
    
    def _kwp2000_frame(self, data: bytes) -> bytes:
        """Format data for KWP2000 transmission"""
        # Similar to ISO 9141-2 but with different addressing
        header = b'\x80\x10\xF1'
        length = len(data).to_bytes(1, 'big')
        checksum = self._kwp2000_checksum(header + length + data)
        return header + length + data + checksum
    
    def _kwp2000_checksum(self, data: bytes) -> bytes:
        """Calculate KWP2000 checksum"""
        return (sum(data) & 0xFF).to_bytes(1, 'big')
    
    def _can_init(self) -> bool:
        """CAN initialization"""
        print("Performing CAN initialization")
        # Set up CAN filters and timing
        return True
    
    def _can_frame(self, data: bytes) -> bytes:
        """Format data for CAN transmission"""
        # CAN frames are handled by the CAN controller
        # This would typically interface with ISO-TP
        return data
    
    def _j1850_init(self) -> bool:
        """SAE J1850 initialization"""
        print("Performing SAE J1850 initialization")
        return True
    
    def _j1850_frame(self, data: bytes) -> bytes:
        """Format data for SAE J1850 transmission"""
        # J1850 specific framing
        header = b'\x48\x6B\x10'
        checksum = self._j1850_checksum(header + data)
        return header + data + checksum
    
    def _j1850_checksum(self, data: bytes) -> bytes:
        """Calculate SAE J1850 checksum (CRC-8)"""
        # Simplified CRC-8 calculation
        crc = 0xFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x1D
                else:
                    crc <<= 1
                crc &= 0xFF
        return crc.to_bytes(1, 'big')


# Reference patterns for main project learning
OBDLIB_REFERENCE_PATTERNS = {
    "command_structure": {
        "mode": "int",
        "pid": "int", 
        "response_length": "int",
        "decoder_function": "callable",
        "unit": "string"
    },
    
    "protocol_detection": {
        "auto_detection_sequence": [
            "try_can_11bit",
            "try_can_29bit", 
            "try_kwp2000_fast",
            "try_kwp2000_slow",
            "try_iso9141",
            "try_j1850_pwm",
            "try_j1850_vpw"
        ]
    },
    
    "error_handling": {
        "no_data": 0x7F,
        "unable_to_connect": "timeout",
        "protocol_mismatch": "invalid_response",
        "checksum_error": "data_corruption"
    },
    
    "response_validation": {
        "check_length": True,
        "verify_checksum": True,
        "validate_mode_echo": True,
        "timeout_handling": True
    }
}


if __name__ == "__main__":
    # Example usage for reference
    db = OBDCommandDatabase()
    rpm_cmd = db.get_command("ENGINE_RPM")
    if rpm_cmd:
        print(f"Command: {rpm_cmd.name}")
        print(f"Mode: 0x{rpm_cmd.mode:02X}")
        print(f"PID: 0x{rpm_cmd.pid:02X}")
        
        # Example response decoding
        mock_response = b'\x1F\x40'  # 8000 RPM / 4 = 2000 RPM
        decoded_value = rpm_cmd.decoder(mock_response)
        print(f"Decoded RPM: {decoded_value}")
    
    # Show protocol handling
    handler = OBDProtocolHandler()
    print("\nSupported protocols:")
    for protocol in OBDProtocol:
        if protocol in handler.protocol_configs:
            config = handler.protocol_configs[protocol]
            print(f"- {protocol.value}: {config['baud_rate']} baud")
