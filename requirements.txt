# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# OBD2 and CAN communication
obd==0.7.1
python-can==4.3.1
cantools==39.4.0
isotp==1.7.1

# AI and LLM integration
openai==1.3.7
llama-cpp-python==0.2.20
transformers==4.36.0
torch==2.1.1

# Database and data management
sqlalchemy==2.0.23
alembic==1.13.0
sqlite3
redis==5.0.1
pandas==2.1.4

# Serial communication for OBD adapters
pyserial==3.5
pyserial-asyncio==0.6

# Bluetooth support
pybluez==0.23
bleak==0.21.1

# HTTP client for external APIs
httpx==0.25.2
aiohttp==3.9.1

# Data validation and parsing
marshmallow==3.20.1
jsonschema==4.20.0

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Configuration management
python-dotenv==1.0.0
pyyaml==6.0.1

# Async support
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# Data visualization (optional)
matplotlib==3.8.2
plotly==5.17.0

# Vehicle-specific libraries
j1939==1.0.2
