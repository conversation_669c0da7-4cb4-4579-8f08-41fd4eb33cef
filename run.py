#!/usr/bin/env python3
"""
OBD2 AI Diagnostic System Startup Script
"""
import os
import sys
import argparse
import uvicorn
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.config import settings


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="OBD2 AI Diagnostic System")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="info", help="Log level")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Create data directory if it doesn't exist
    data_dir = Path("app/data")
    data_dir.mkdir(exist_ok=True)
    
    print("🚗 Starting OBD2 AI Diagnostic System")
    print(f"📡 API will be available at: http://{args.host}:{args.port}")
    print(f"📚 API Documentation: http://{args.host}:{args.port}/docs")
    print(f"🔍 Health Check: http://{args.host}:{args.port}/health")
    print()
    
    # Configuration summary
    print("⚙️  Configuration:")
    print(f"   Debug Mode: {args.debug or settings.debug}")
    print(f"   Log Level: {args.log_level}")
    print(f"   OBD Port: {settings.obd_port or 'Auto-detect'}")
    print(f"   CAN Interface: {settings.can_interface}")
    print(f"   AI Engine: {'OpenAI' if settings.openai_api_key else 'Local LLM' if settings.use_local_llm else 'Fallback'}")
    print()
    
    # Start the server
    try:
        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload or args.debug,
            log_level=args.log_level,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down OBD2 AI Diagnostic System")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
