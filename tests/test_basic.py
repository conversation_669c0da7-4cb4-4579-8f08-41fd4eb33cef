"""
Basic tests for OBD2 AI Diagnostic System
"""
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.obd_interface.dtc_parser import DTCParser
from app.ai_engine.prompt_builder import PromptBuilder, VehicleContext
from app.brand_profiles.toyota import ToyotaProfile


client = TestClient(app)


def test_root_endpoint():
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "name" in data
    assert "version" in data
    assert "status" in data
    assert data["status"] == "running"


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "healthy"


def test_api_info():
    """Test API info endpoint"""
    response = client.get("/api/v1/info")
    assert response.status_code == 200
    data = response.json()
    assert "title" in data
    assert "endpoints" in data
    assert "features" in data


def test_dtc_parser():
    """Test DTC parser functionality"""
    parser = DTCParser()
    
    # Test known DTC
    dtc_info = parser.parse_dtc("P0171")
    assert dtc_info.code == "P0171"
    assert "lean" in dtc_info.description.lower()
    assert dtc_info.category == "Powertrain"
    assert len(dtc_info.possible_causes) > 0
    
    # Test unknown DTC
    unknown_dtc = parser.parse_dtc("P9999")
    assert unknown_dtc.code == "P9999"
    assert "unknown" in unknown_dtc.description.lower()


def test_prompt_builder():
    """Test AI prompt builder"""
    builder = PromptBuilder()
    
    # Test vehicle context
    vehicle_context = VehicleContext(
        make="Toyota",
        model="Prius",
        year=2015,
        mileage=100000
    )
    
    # Test basic prompt building
    from app.obd_interface.dtc_parser import DTCInfo
    from app.ai_engine.prompt_builder import DiagnosticContext
    
    dtc_info = DTCInfo(
        code="P0171",
        description="System Too Lean (Bank 1)",
        category="Powertrain",
        severity="medium",
        system="Fuel and Air Metering",
        possible_causes=["Vacuum leak", "Faulty MAF sensor"],
        repair_hints=["Check for vacuum leaks", "Clean MAF sensor"],
        related_pids=["0x10"]
    )
    
    diagnostic_context = DiagnosticContext(
        dtcs=[dtc_info],
        parameters=[],
        vehicle_info=vehicle_context
    )
    
    prompt = builder.build_dtc_analysis_prompt(diagnostic_context)
    assert len(prompt) > 0
    assert "Toyota" in prompt
    assert "Prius" in prompt
    assert "P0171" in prompt


def test_toyota_profile():
    """Test Toyota brand profile"""
    profile = ToyotaProfile()
    
    # Test common issues
    issues = profile.get_common_issues("Prius", 2015)
    assert len(issues) > 0
    assert any("hybrid" in issue.lower() for issue in issues)
    
    # Test brand knowledge
    knowledge = profile.get_brand_knowledge("Prius", 2015)
    assert "common_issues" in knowledge
    assert "recalls" in knowledge
    assert "service_bulletins" in knowledge


def test_connection_status():
    """Test connection status endpoint"""
    response = client.get("/api/v1/connection/status")
    assert response.status_code == 200
    data = response.json()
    assert "connected" in data
    # Should be False since no actual OBD adapter is connected in tests
    assert data["connected"] == False


def test_list_ports():
    """Test list available ports endpoint"""
    response = client.get("/api/v1/connection/ports")
    assert response.status_code == 200
    data = response.json()
    assert "ports" in data
    assert isinstance(data["ports"], list)


def test_system_status():
    """Test system status endpoint"""
    response = client.get("/api/v1/status")
    assert response.status_code == 200
    data = response.json()
    assert "obd_connected" in data
    assert "can_connected" in data
    assert "ai_available" in data
    assert "database_connected" in data
    assert "system_health" in data


def test_list_brands():
    """Test list supported brands endpoint"""
    response = client.get("/api/v1/brands")
    assert response.status_code == 200
    data = response.json()
    assert "brands" in data
    assert len(data["brands"]) > 0
    
    # Check for expected brands
    brand_names = [brand["name"] for brand in data["brands"]]
    assert "Toyota" in brand_names


def test_get_brand_profile():
    """Test get brand profile endpoint"""
    response = client.get("/api/v1/brands/toyota?model=Prius&year=2015")
    assert response.status_code == 200
    data = response.json()
    assert "brand_name" in data
    assert "common_issues" in data
    assert "recalls" in data
    assert len(data["common_issues"]) > 0


def test_scan_without_connection():
    """Test diagnostic scan without OBD connection"""
    response = client.post("/api/v1/scan", json={
        "include_dtcs": True,
        "include_parameters": True
    })
    assert response.status_code == 400  # Should fail without connection


def test_analyze_with_dtcs():
    """Test AI analysis with provided DTCs"""
    response = client.post("/api/v1/analyze?dtcs=P0171&dtcs=P0300", json={
        "vehicle_info": {
            "make": "Toyota",
            "model": "Prius",
            "year": 2015,
            "mileage": 100000
        },
        "include_brand_specific": True,
        "include_cost_estimate": True,
        "include_maintenance_plan": True
    })
    assert response.status_code == 200
    data = response.json()
    assert "summary" in data
    assert "confidence_score" in data
    assert "repair_recommendations" in data
    assert len(data["repair_recommendations"]) > 0


if __name__ == "__main__":
    pytest.main([__file__])
