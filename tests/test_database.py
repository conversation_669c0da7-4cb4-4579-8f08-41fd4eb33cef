"""
Database tests for OBD2 AI Diagnostic System
"""
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.database.models import Base, DTCCode, VehicleProfile, DiagnosticSession
from app.database.database import DatabaseManager, DTCRepository
from app.database.seed_data import seed_database


@pytest.fixture
def test_db():
    """Create test database"""
    # Use in-memory SQLite for testing
    engine = create_engine("sqlite:///:memory:", echo=False)
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db_manager = DatabaseManager("sqlite:///:memory:")
    db_manager.engine = engine
    db_manager.SessionLocal = SessionLocal
    
    return db_manager


def test_database_initialization(test_db):
    """Test database initialization"""
    assert test_db.engine is not None
    assert test_db.SessionLocal is not None
    assert test_db.health_check() == True


def test_dtc_repository_operations(test_db):
    """Test DTC repository operations"""
    dtc_repo = DTCRepository(test_db)
    
    # Test creating DTC
    dtc_data = {
        'code': 'P0171',
        'description': 'System Too Lean (Bank 1)',
        'category': 'Powertrain',
        'severity': 'medium',
        'system': 'Fuel and Air Metering',
        'possible_causes': ['Vacuum leak', 'Faulty MAF sensor'],
        'repair_hints': ['Check for vacuum leaks', 'Clean MAF sensor'],
        'related_pids': ['0x10'],
        'freeze_frame_required': True,
        'obd2_standard': True
    }
    
    created_dtc = dtc_repo.create_dtc(dtc_data)
    assert created_dtc.code == 'P0171'
    assert created_dtc.severity == 'medium'
    
    # Test retrieving DTC
    retrieved_dtc = dtc_repo.get_dtc_by_code('P0171')
    assert retrieved_dtc is not None
    assert retrieved_dtc.description == 'System Too Lean (Bank 1)'
    
    # Test searching DTCs
    search_results = dtc_repo.search_dtcs(search_term='lean')
    assert len(search_results) > 0
    assert any(dtc.code == 'P0171' for dtc in search_results)
    
    # Test getting DTCs by category
    powertrain_dtcs = dtc_repo.get_dtcs_by_category('Powertrain')
    assert len(powertrain_dtcs) > 0
    assert all(dtc.category == 'Powertrain' for dtc in powertrain_dtcs)


def test_bulk_dtc_insertion(test_db):
    """Test bulk DTC insertion"""
    dtc_repo = DTCRepository(test_db)
    
    dtc_list = [
        {
            'code': 'P0300',
            'description': 'Random/Multiple Cylinder Misfire Detected',
            'category': 'Powertrain',
            'severity': 'high',
            'system': 'Ignition System',
            'possible_causes': ['Faulty spark plugs', 'Ignition coils'],
            'repair_hints': ['Replace spark plugs', 'Test ignition coils'],
            'related_pids': ['0x0C'],
            'freeze_frame_required': True,
            'obd2_standard': True
        },
        {
            'code': 'P0420',
            'description': 'Catalyst System Efficiency Below Threshold',
            'category': 'Powertrain',
            'severity': 'medium',
            'system': 'Emission Control',
            'possible_causes': ['Faulty catalytic converter'],
            'repair_hints': ['Replace catalytic converter'],
            'related_pids': ['0x14', '0x15'],
            'freeze_frame_required': True,
            'obd2_standard': True
        }
    ]
    
    inserted_count = dtc_repo.bulk_insert_dtcs(dtc_list)
    assert inserted_count == 2
    
    # Verify insertion
    p0300 = dtc_repo.get_dtc_by_code('P0300')
    assert p0300 is not None
    assert p0300.severity == 'high'
    
    p0420 = dtc_repo.get_dtc_by_code('P0420')
    assert p0420 is not None
    assert p0420.system == 'Emission Control'


def test_vehicle_profile_operations(test_db):
    """Test vehicle profile operations"""
    from app.database.database import VehicleRepository
    
    vehicle_repo = VehicleRepository(test_db)
    
    # Test creating vehicle
    vehicle_data = {
        'make': 'Toyota',
        'model': 'Prius',
        'year': 2015,
        'vin': '1NXBR32E25Z123456',
        'engine_type': '1.8L Hybrid',
        'fuel_type': 'hybrid',
        'current_mileage': 85000
    }
    
    created_vehicle = vehicle_repo.create_vehicle(vehicle_data)
    assert created_vehicle.make == 'Toyota'
    assert created_vehicle.model == 'Prius'
    assert created_vehicle.year == 2015
    
    # Test retrieving vehicle by VIN
    retrieved_vehicle = vehicle_repo.get_vehicle_by_vin('1NXBR32E25Z123456')
    assert retrieved_vehicle is not None
    assert retrieved_vehicle.make == 'Toyota'
    
    # Test searching vehicles
    toyota_vehicles = vehicle_repo.search_vehicles(make='Toyota')
    assert len(toyota_vehicles) > 0
    assert all(vehicle.make == 'Toyota' for vehicle in toyota_vehicles)


def test_diagnostic_session_operations(test_db):
    """Test diagnostic session operations"""
    from app.database.database import DiagnosticSessionRepository, VehicleRepository
    
    # First create a vehicle
    vehicle_repo = VehicleRepository(test_db)
    vehicle_data = {
        'make': 'BMW',
        'model': '3 Series',
        'year': 2018,
        'vin': 'WBA8E1C50JA123456',
        'engine_type': '2.0L Turbo',
        'fuel_type': 'gasoline'
    }
    vehicle = vehicle_repo.create_vehicle(vehicle_data)
    
    # Create diagnostic session
    session_repo = DiagnosticSessionRepository(test_db)
    session_data = {
        'session_id': 'test_session_001',
        'vehicle_id': vehicle.id,
        'connection_type': 'usb',
        'protocol_used': 'OBD2',
        'dtc_count': 2,
        'dtc_codes_found': ['P0171', 'P0300'],
        'status': 'completed'
    }
    
    created_session = session_repo.create_session(session_data)
    assert created_session.session_id == 'test_session_001'
    assert created_session.vehicle_id == vehicle.id
    assert created_session.dtc_count == 2
    
    # Test retrieving session
    retrieved_session = session_repo.get_session_by_id('test_session_001')
    assert retrieved_session is not None
    assert retrieved_session.connection_type == 'usb'
    
    # Test getting vehicle sessions
    vehicle_sessions = session_repo.get_vehicle_sessions(vehicle.id)
    assert len(vehicle_sessions) > 0
    assert vehicle_sessions[0].session_id == 'test_session_001'


def test_database_seeding(test_db):
    """Test database seeding functionality"""
    # Note: This would normally use the actual seed_database function
    # but we'll test the concept with a simplified version
    
    dtc_repo = DTCRepository(test_db)
    
    # Check initial state
    initial_count = len(dtc_repo.search_dtcs(limit=1000))
    
    # Add some test data
    test_dtcs = [
        {
            'code': 'P0001',
            'description': 'Test DTC 1',
            'category': 'Powertrain',
            'severity': 'low',
            'system': 'Test System',
            'possible_causes': ['Test cause'],
            'repair_hints': ['Test hint'],
            'related_pids': [],
            'freeze_frame_required': False,
            'obd2_standard': True
        },
        {
            'code': 'P0002',
            'description': 'Test DTC 2',
            'category': 'Body',
            'severity': 'medium',
            'system': 'Test System',
            'possible_causes': ['Test cause'],
            'repair_hints': ['Test hint'],
            'related_pids': [],
            'freeze_frame_required': False,
            'obd2_standard': True
        }
    ]
    
    inserted_count = dtc_repo.bulk_insert_dtcs(test_dtcs)
    assert inserted_count == 2
    
    # Verify seeding
    final_count = len(dtc_repo.search_dtcs(limit=1000))
    assert final_count == initial_count + 2


def test_database_relationships(test_db):
    """Test database relationships between tables"""
    from app.database.database import VehicleRepository, DiagnosticSessionRepository
    
    # Create vehicle and session with relationship
    vehicle_repo = VehicleRepository(test_db)
    session_repo = DiagnosticSessionRepository(test_db)
    
    vehicle_data = {
        'make': 'Volkswagen',
        'model': 'Golf',
        'year': 2020,
        'vin': 'WVWZZZ1JZ1W123456'
    }
    vehicle = vehicle_repo.create_vehicle(vehicle_data)
    
    session_data = {
        'session_id': 'relationship_test',
        'vehicle_id': vehicle.id,
        'dtc_count': 1,
        'status': 'completed'
    }
    session = session_repo.create_session(session_data)
    
    # Test relationship access
    with test_db.get_session() as db_session:
        # Get vehicle with sessions
        vehicle_with_sessions = db_session.query(VehicleProfile).filter(
            VehicleProfile.id == vehicle.id
        ).first()
        
        assert vehicle_with_sessions is not None
        # Note: In a real test, we'd check the relationship
        # assert len(vehicle_with_sessions.diagnostic_sessions) > 0


if __name__ == "__main__":
    pytest.main([__file__])
